<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="用户昵称" prop="userNickName">
        <el-input
          v-model="queryParams.userNickName"
          placeholder="请输入用户昵称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="审核状态" prop="auditStatus">
        <el-select v-model="queryParams.auditStatus" placeholder="请选择审核状态" clearable>
          <el-option label="待审核" value="0" />
          <el-option label="审核通过" value="1" />
          <el-option label="审核拒绝" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="审核人" prop="auditBy">
        <el-input
          v-model="queryParams.auditBy"
          placeholder="请输入审核人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['miniapp:haitang:project-registration:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['miniapp:haitang:project-registration:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="projectRegistrationList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="报名ID" align="center" prop="registrationId" />
      <el-table-column label="用户昵称" align="center" prop="userNickName" />
      <el-table-column label="审核状态" align="center" prop="auditStatus">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.auditStatus === '0'" type="warning">待审核</el-tag>
          <el-tag v-else-if="scope.row.auditStatus === '1'" type="success">审核通过</el-tag>
          <el-tag v-else-if="scope.row.auditStatus === '2'" type="danger">审核拒绝</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="审核人" align="center" prop="auditBy" />
      <el-table-column label="审核时间" align="center" prop="auditTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.auditTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['miniapp:haitang:project-registration:query']"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-check"
            @click="handleAudit(scope.row)"
            v-hasPermi="['miniapp:haitang:project-registration:audit']"
            v-if="scope.row.auditStatus === '0'"
          >审核</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['miniapp:haitang:project-registration:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 查看报名详情对话框 -->
    <el-dialog title="报名详情" :visible.sync="viewOpen" width="800px" append-to-body>
      <el-form ref="viewForm" :model="viewForm" label-width="100px">
        <el-form-item label="用户昵称">
          <span>{{ viewForm.userNickName }}</span>
        </el-form-item>
        <el-form-item label="报名数据">
          <pre style="white-space: pre-wrap; word-wrap: break-word;">{{ formatFormData(viewForm.formData) }}</pre>
        </el-form-item>
        <el-form-item label="审核状态">
          <el-tag v-if="viewForm.auditStatus === '0'" type="warning">待审核</el-tag>
          <el-tag v-else-if="viewForm.auditStatus === '1'" type="success">审核通过</el-tag>
          <el-tag v-else-if="viewForm.auditStatus === '2'" type="danger">审核拒绝</el-tag>
        </el-form-item>
        <el-form-item label="审核备注" v-if="viewForm.auditRemark">
          <span>{{ viewForm.auditRemark }}</span>
        </el-form-item>
        <el-form-item label="审核人" v-if="viewForm.auditBy">
          <span>{{ viewForm.auditBy }}</span>
        </el-form-item>
        <el-form-item label="审核时间" v-if="viewForm.auditTime">
          <span>{{ parseTime(viewForm.auditTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </el-form-item>
        <el-form-item label="创建时间">
          <span>{{ parseTime(viewForm.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="viewOpen = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog title="审核报名" :visible.sync="auditOpen" width="500px" append-to-body>
      <el-form ref="auditForm" :model="auditForm" :rules="auditRules" label-width="80px">
        <el-form-item label="审核结果" prop="auditStatus">
          <el-radio-group v-model="auditForm.auditStatus">
            <el-radio label="1">审核通过</el-radio>
            <el-radio label="2">审核拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核备注" prop="auditRemark">
          <el-input v-model="auditForm.auditRemark" type="textarea" placeholder="请输入审核备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAudit">确 定</el-button>
        <el-button @click="auditOpen = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listProjectRegistration, getProjectRegistration, delProjectRegistration, auditProjectRegistration } from "@/api/miniapp/haitang/projectRegistration";

export default {
  name: "ProjectRegistration",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 天大海棠杯项目报名记录表格数据
      projectRegistrationList: [],
      // 是否显示查看详情弹出层
      viewOpen: false,
      // 是否显示审核弹出层
      auditOpen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userNickName: null,
        auditStatus: null,
        auditBy: null,
      },
      // 查看表单参数
      viewForm: {},
      // 审核表单参数
      auditForm: {},
      // 审核表单校验
      auditRules: {
        auditStatus: [
          { required: true, message: "审核结果不能为空", trigger: "change" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询天大海棠杯项目报名记录列表 */
    getList() {
      this.loading = true;
      listProjectRegistration(this.queryParams).then(response => {
        this.projectRegistrationList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.registrationId)
      this.multiple = !selection.length
    },
    /** 查看按钮操作 */
    handleView(row) {
      getProjectRegistration(row.registrationId).then(response => {
        this.viewForm = response.data;
        this.viewOpen = true;
      });
    },
    /** 审核按钮操作 */
    handleAudit(row) {
      this.auditForm = {
        registrationId: row.registrationId,
        auditStatus: null,
        auditRemark: null
      };
      this.auditOpen = true;
    },
    /** 提交审核 */
    submitAudit() {
      this.$refs["auditForm"].validate(valid => {
        if (valid) {
          auditProjectRegistration(this.auditForm).then(response => {
            this.$modal.msgSuccess("审核成功");
            this.auditOpen = false;
            this.getList();
          });
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const registrationIds = row.registrationId || this.ids;
      this.$modal.confirm('是否确认删除天大海棠杯项目报名记录编号为"' + registrationIds + '"的数据项？').then(function() {
        return delProjectRegistration(registrationIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('miniapp/haitang/project-registration/export', {
        ...this.queryParams
      }, `project-registration_${new Date().getTime()}.xlsx`)
    },
    /** 格式化表单数据 */
    formatFormData(formData) {
      if (!formData) return '';
      try {
        return JSON.stringify(JSON.parse(formData), null, 2);
      } catch (e) {
        return formData;
      }
    }
  }
};
</script>
