package com.ruoyi.miniapp.service.impl;

import java.util.Date;
import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.miniapp.mapper.HaitangProjectRegistrationMapper;
import com.ruoyi.miniapp.domain.HaitangProjectRegistration;
import com.ruoyi.miniapp.service.IHaitangProjectRegistrationService;

/**
 * 天大海棠杯项目报名记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
@Service
public class HaitangProjectRegistrationServiceImpl implements IHaitangProjectRegistrationService 
{
    @Autowired
    private HaitangProjectRegistrationMapper haitangProjectRegistrationMapper;

    /**
     * 查询天大海棠杯项目报名记录
     * 
     * @param registrationId 天大海棠杯项目报名记录主键
     * @return 天大海棠杯项目报名记录
     */
    @Override
    public HaitangProjectRegistration selectHaitangProjectRegistrationByRegistrationId(Long registrationId)
    {
        return haitangProjectRegistrationMapper.selectHaitangProjectRegistrationByRegistrationId(registrationId);
    }

    /**
     * 查询天大海棠杯项目报名记录列表
     * 
     * @param haitangProjectRegistration 天大海棠杯项目报名记录
     * @return 天大海棠杯项目报名记录
     */
    @Override
    public List<HaitangProjectRegistration> selectHaitangProjectRegistrationList(HaitangProjectRegistration haitangProjectRegistration)
    {
        return haitangProjectRegistrationMapper.selectHaitangProjectRegistrationList(haitangProjectRegistration);
    }

    /**
     * 新增天大海棠杯项目报名记录
     * 
     * @param haitangProjectRegistration 天大海棠杯项目报名记录
     * @return 结果
     */
    @Override
    public int insertHaitangProjectRegistration(HaitangProjectRegistration haitangProjectRegistration)
    {
        haitangProjectRegistration.setCreateTime(DateUtils.getNowDate());
        return haitangProjectRegistrationMapper.insertHaitangProjectRegistration(haitangProjectRegistration);
    }

    /**
     * 修改天大海棠杯项目报名记录
     * 
     * @param haitangProjectRegistration 天大海棠杯项目报名记录
     * @return 结果
     */
    @Override
    public int updateHaitangProjectRegistration(HaitangProjectRegistration haitangProjectRegistration)
    {
        haitangProjectRegistration.setUpdateTime(DateUtils.getNowDate());
        return haitangProjectRegistrationMapper.updateHaitangProjectRegistration(haitangProjectRegistration);
    }

    /**
     * 批量删除天大海棠杯项目报名记录
     * 
     * @param registrationIds 需要删除的天大海棠杯项目报名记录主键
     * @return 结果
     */
    @Override
    public int deleteHaitangProjectRegistrationByRegistrationIds(Long[] registrationIds)
    {
        return haitangProjectRegistrationMapper.deleteHaitangProjectRegistrationByRegistrationIds(registrationIds);
    }

    /**
     * 删除天大海棠杯项目报名记录信息
     * 
     * @param registrationId 天大海棠杯项目报名记录主键
     * @return 结果
     */
    @Override
    public int deleteHaitangProjectRegistrationByRegistrationId(Long registrationId)
    {
        return haitangProjectRegistrationMapper.deleteHaitangProjectRegistrationByRegistrationId(registrationId);
    }

    /**
     * 根据用户ID和配置ID查询报名记录
     * 
     * @param userId 用户ID
     * @param configId 配置ID
     * @return 报名记录
     */
    @Override
    public HaitangProjectRegistration selectByUserIdAndConfigId(Long userId, Long configId)
    {
        return haitangProjectRegistrationMapper.selectByUserIdAndConfigId(userId, configId);
    }

    /**
     * 审核报名记录
     * 
     * @param haitangProjectRegistration 报名记录
     * @return 结果
     */
    @Override
    public int auditRegistration(HaitangProjectRegistration haitangProjectRegistration)
    {
        haitangProjectRegistration.setAuditTime(new Date());
        haitangProjectRegistration.setAuditBy(SecurityUtils.getUsername());
        return haitangProjectRegistrationMapper.auditRegistration(haitangProjectRegistration);
    }

    /**
     * 小程序端提交报名
     * 
     * @param haitangProjectRegistration 报名记录
     * @return 结果
     */
    @Override
    public int submitRegistration(HaitangProjectRegistration haitangProjectRegistration)
    {
        // 检查是否已经报名
        HaitangProjectRegistration existingRegistration = selectByUserIdAndConfigId(
            haitangProjectRegistration.getUserId(), 
            haitangProjectRegistration.getConfigId()
        );
        
        if (existingRegistration != null) {
            throw new RuntimeException("您已经报名过该项目，请勿重复报名");
        }
        
        // 设置默认审核状态为待审核
        haitangProjectRegistration.setAuditStatus("0");
        haitangProjectRegistration.setCreateTime(DateUtils.getNowDate());
        
        return haitangProjectRegistrationMapper.insertHaitangProjectRegistration(haitangProjectRegistration);
    }
}
