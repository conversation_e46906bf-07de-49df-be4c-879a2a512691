-- 天大海棠杯项目报名模块菜单权限配置

-- 查找天大海棠杯创赛专区的父菜单ID（假设已存在）
-- 如果不存在，需要先创建父菜单
-- INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
-- VALUES('天大海棠杯创赛专区', '0', '5', 'haitang', null, 1, 0, 'M', '0', '0', '', 'star', 'admin', sysdate(), '', null, '天大海棠杯创赛专区目录');

-- 获取天大海棠杯创赛专区菜单ID（假设为2000）
SET @haitangParentId = 2000;

-- 项目报名配置菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('项目报名配置', @haitangParentId, '1', 'project-config', 'miniapp/haitang/project-config/index', 1, 0, 'C', '0', '0', 'miniapp:haitang:project-config:list', 'form', 'admin', sysdate(), '', null, '项目报名配置菜单');

-- 获取项目报名配置菜单ID
SELECT @projectConfigMenuId := LAST_INSERT_ID();

-- 项目报名配置按钮权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('项目报名配置查询', @projectConfigMenuId, '1', '#', '', 1, 0, 'F', '0', '0', 'miniapp:haitang:project-config:query', '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('项目报名配置新增', @projectConfigMenuId, '2', '#', '', 1, 0, 'F', '0', '0', 'miniapp:haitang:project-config:add', '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('项目报名配置修改', @projectConfigMenuId, '3', '#', '', 1, 0, 'F', '0', '0', 'miniapp:haitang:project-config:edit', '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('项目报名配置删除', @projectConfigMenuId, '4', '#', '', 1, 0, 'F', '0', '0', 'miniapp:haitang:project-config:remove', '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('项目报名配置导出', @projectConfigMenuId, '5', '#', '', 1, 0, 'F', '0', '0', 'miniapp:haitang:project-config:export', '#', 'admin', sysdate(), '', null, '');

-- 项目报名记录菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('项目报名记录', @haitangParentId, '2', 'project-registration', 'miniapp/haitang/project-registration/index', 1, 0, 'C', '0', '0', 'miniapp:haitang:project-registration:list', 'list', 'admin', sysdate(), '', null, '项目报名记录菜单');

-- 获取项目报名记录菜单ID
SELECT @projectRegistrationMenuId := LAST_INSERT_ID();

-- 项目报名记录按钮权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('项目报名记录查询', @projectRegistrationMenuId, '1', '#', '', 1, 0, 'F', '0', '0', 'miniapp:haitang:project-registration:query', '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('项目报名记录新增', @projectRegistrationMenuId, '2', '#', '', 1, 0, 'F', '0', '0', 'miniapp:haitang:project-registration:add', '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('项目报名记录修改', @projectRegistrationMenuId, '3', '#', '', 1, 0, 'F', '0', '0', 'miniapp:haitang:project-registration:edit', '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('项目报名记录删除', @projectRegistrationMenuId, '4', '#', '', 1, 0, 'F', '0', '0', 'miniapp:haitang:project-registration:remove', '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('项目报名记录导出', @projectRegistrationMenuId, '5', '#', '', 1, 0, 'F', '0', '0', 'miniapp:haitang:project-registration:export', '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('项目报名记录审核', @projectRegistrationMenuId, '6', '#', '', 1, 0, 'F', '0', '0', 'miniapp:haitang:project-registration:audit', '#', 'admin', sysdate(), '', null, '');

-- 注意：执行此脚本前，请确认天大海棠杯创赛专区的父菜单ID，并相应修改 @haitangParentId 的值
-- 可以通过以下SQL查询现有菜单结构：
-- SELECT menu_id, menu_name, parent_id FROM sys_menu WHERE menu_name LIKE '%海棠%' OR menu_name LIKE '%创赛%';
