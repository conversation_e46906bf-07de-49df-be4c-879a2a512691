{"remainingRequest": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\haitang\\project-registration\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\haitang\\project-registration\\index.vue", "mtime": 1754272686655}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753843467163}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753843480362}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753843467163}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753843484659}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3RQcm9qZWN0UmVnaXN0cmF0aW9uLCBnZXRQcm9qZWN0UmVnaXN0cmF0aW9uLCBkZWxQcm9qZWN0UmVnaXN0cmF0aW9uLCBhdWRpdFByb2plY3RSZWdpc3RyYXRpb24gfSBmcm9tICJAL2FwaS9taW5pYXBwL2hhaXRhbmcvcHJvamVjdFJlZ2lzdHJhdGlvbiI7CgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIlByb2plY3RSZWdpc3RyYXRpb24iLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDpga7nvanlsYIKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgLy8g6YCJ5Lit5pWw57uECiAgICAgIGlkczogW10sCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqAogICAgICBtdWx0aXBsZTogdHJ1ZSwKICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2CiAgICAgIHNob3dTZWFyY2g6IHRydWUsCiAgICAgIC8vIOaAu+adoeaVsAogICAgICB0b3RhbDogMCwKICAgICAgLy8g5aSp5aSn5rW35qOg5p2v6aG555uu5oql5ZCN6K6w5b2V6KGo5qC85pWw5o2uCiAgICAgIHByb2plY3RSZWdpc3RyYXRpb25MaXN0OiBbXSwKICAgICAgLy8g5piv5ZCm5pi+56S65p+l55yL6K+m5oOF5by55Ye65bGCCiAgICAgIHZpZXdPcGVuOiBmYWxzZSwKICAgICAgLy8g5piv5ZCm5pi+56S65a6h5qC45by55Ye65bGCCiAgICAgIGF1ZGl0T3BlbjogZmFsc2UsCiAgICAgIC8vIOafpeivouWPguaVsAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIHVzZXJOaWNrTmFtZTogbnVsbCwKICAgICAgICBhdWRpdFN0YXR1czogbnVsbCwKICAgICAgICBhdWRpdEJ5OiBudWxsLAogICAgICB9LAogICAgICAvLyDmn6XnnIvooajljZXlj4LmlbAKICAgICAgdmlld0Zvcm06IHt9LAogICAgICAvLyDlrqHmoLjooajljZXlj4LmlbAKICAgICAgYXVkaXRGb3JtOiB7fSwKICAgICAgLy8g5a6h5qC46KGo5Y2V5qCh6aqMCiAgICAgIGF1ZGl0UnVsZXM6IHsKICAgICAgICBhdWRpdFN0YXR1czogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuWuoeaguOe7k+aenOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJjaGFuZ2UiIH0KICAgICAgICBdLAogICAgICB9CiAgICB9OwogIH0sCiAgY3JlYXRlZCgpIHsKICAgIHRoaXMuZ2V0TGlzdCgpOwogIH0sCiAgbWV0aG9kczogewogICAgLyoqIOafpeivouWkqeWkp+a1t+ajoOadr+mhueebruaKpeWQjeiusOW9leWIl+ihqCAqLwogICAgZ2V0TGlzdCgpIHsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgbGlzdFByb2plY3RSZWdpc3RyYXRpb24odGhpcy5xdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5wcm9qZWN0UmVnaXN0cmF0aW9uTGlzdCA9IHJlc3BvbnNlLnJvd3M7CiAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOwogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVRdWVyeSgpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLwogICAgcmVzZXRRdWVyeSgpIHsKICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOwogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7CiAgICB9LAogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0ucmVnaXN0cmF0aW9uSWQpCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aAogICAgfSwKICAgIC8qKiDmn6XnnIvmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZVZpZXcocm93KSB7CiAgICAgIGdldFByb2plY3RSZWdpc3RyYXRpb24ocm93LnJlZ2lzdHJhdGlvbklkKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLnZpZXdGb3JtID0gcmVzcG9uc2UuZGF0YTsKICAgICAgICB0aGlzLnZpZXdPcGVuID0gdHJ1ZTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOWuoeaguOaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlQXVkaXQocm93KSB7CiAgICAgIHRoaXMuYXVkaXRGb3JtID0gewogICAgICAgIHJlZ2lzdHJhdGlvbklkOiByb3cucmVnaXN0cmF0aW9uSWQsCiAgICAgICAgYXVkaXRTdGF0dXM6IG51bGwsCiAgICAgICAgYXVkaXRSZW1hcms6IG51bGwKICAgICAgfTsKICAgICAgdGhpcy5hdWRpdE9wZW4gPSB0cnVlOwogICAgfSwKICAgIC8qKiDmj5DkuqTlrqHmoLggKi8KICAgIHN1Ym1pdEF1ZGl0KCkgewogICAgICB0aGlzLiRyZWZzWyJhdWRpdEZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBhdWRpdFByb2plY3RSZWdpc3RyYXRpb24odGhpcy5hdWRpdEZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLlrqHmoLjmiJDlip8iKTsKICAgICAgICAgICAgdGhpcy5hdWRpdE9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZURlbGV0ZShyb3cpIHsKICAgICAgY29uc3QgcmVnaXN0cmF0aW9uSWRzID0gcm93LnJlZ2lzdHJhdGlvbklkIHx8IHRoaXMuaWRzOwogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTlpKnlpKfmtbfmo6Dmna/pobnnm67miqXlkI3orrDlvZXnvJblj7fkuLoiJyArIHJlZ2lzdHJhdGlvbklkcyArICci55qE5pWw5o2u6aG577yfJykudGhlbihmdW5jdGlvbigpIHsKICAgICAgICByZXR1cm4gZGVsUHJvamVjdFJlZ2lzdHJhdGlvbihyZWdpc3RyYXRpb25JZHMpOwogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsKICAgICAgfSkuY2F0Y2goKCkgPT4ge30pOwogICAgfSwKICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZUV4cG9ydCgpIHsKICAgICAgdGhpcy5kb3dubG9hZCgnbWluaWFwcC9oYWl0YW5nL3Byb2plY3QtcmVnaXN0cmF0aW9uL2V4cG9ydCcsIHsKICAgICAgICAuLi50aGlzLnF1ZXJ5UGFyYW1zCiAgICAgIH0sIGBwcm9qZWN0LXJlZ2lzdHJhdGlvbl8ke25ldyBEYXRlKCkuZ2V0VGltZSgpfS54bHN4YCkKICAgIH0sCiAgICAvKiog5qC85byP5YyW6KGo5Y2V5pWw5o2uICovCiAgICBmb3JtYXRGb3JtRGF0YShmb3JtRGF0YSkgewogICAgICBpZiAoIWZvcm1EYXRhKSByZXR1cm4gJyc7CiAgICAgIHRyeSB7CiAgICAgICAgcmV0dXJuIEpTT04uc3RyaW5naWZ5KEpTT04ucGFyc2UoZm9ybURhdGEpLCBudWxsLCAyKTsKICAgICAgfSBjYXRjaCAoZSkgewogICAgICAgIHJldHVybiBmb3JtRGF0YTsKICAgICAgfQogICAgfQogIH0KfTsK"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyKA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/miniapp/haitang/project-registration", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"用户昵称\" prop=\"userNickName\">\n        <el-input\n          v-model=\"queryParams.userNickName\"\n          placeholder=\"请输入用户昵称\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"审核状态\" prop=\"auditStatus\">\n        <el-select v-model=\"queryParams.auditStatus\" placeholder=\"请选择审核状态\" clearable>\n          <el-option label=\"待审核\" value=\"0\" />\n          <el-option label=\"审核通过\" value=\"1\" />\n          <el-option label=\"审核拒绝\" value=\"2\" />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"审核人\" prop=\"auditBy\">\n        <el-input\n          v-model=\"queryParams.auditBy\"\n          placeholder=\"请输入审核人\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n          v-hasPermi=\"['miniapp:haitang:project-registration:remove']\"\n        >删除</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"handleExport\"\n          v-hasPermi=\"['miniapp:haitang:project-registration:export']\"\n        >导出</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"projectRegistrationList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"报名ID\" align=\"center\" prop=\"registrationId\" />\n      <el-table-column label=\"用户昵称\" align=\"center\" prop=\"userNickName\" />\n      <el-table-column label=\"审核状态\" align=\"center\" prop=\"auditStatus\">\n        <template slot-scope=\"scope\">\n          <el-tag v-if=\"scope.row.auditStatus === '0'\" type=\"warning\">待审核</el-tag>\n          <el-tag v-else-if=\"scope.row.auditStatus === '1'\" type=\"success\">审核通过</el-tag>\n          <el-tag v-else-if=\"scope.row.auditStatus === '2'\" type=\"danger\">审核拒绝</el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"审核人\" align=\"center\" prop=\"auditBy\" />\n      <el-table-column label=\"审核时间\" align=\"center\" prop=\"auditTime\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.auditTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-view\"\n            @click=\"handleView(scope.row)\"\n            v-hasPermi=\"['miniapp:haitang:project-registration:query']\"\n          >查看</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-check\"\n            @click=\"handleAudit(scope.row)\"\n            v-hasPermi=\"['miniapp:haitang:project-registration:audit']\"\n            v-if=\"scope.row.auditStatus === '0'\"\n          >审核</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n            v-hasPermi=\"['miniapp:haitang:project-registration:remove']\"\n          >删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    \n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 查看报名详情对话框 -->\n    <el-dialog title=\"报名详情\" :visible.sync=\"viewOpen\" width=\"800px\" append-to-body>\n      <el-form ref=\"viewForm\" :model=\"viewForm\" label-width=\"100px\">\n        <el-form-item label=\"用户昵称\">\n          <span>{{ viewForm.userNickName }}</span>\n        </el-form-item>\n        <el-form-item label=\"报名数据\">\n          <pre style=\"white-space: pre-wrap; word-wrap: break-word;\">{{ formatFormData(viewForm.formData) }}</pre>\n        </el-form-item>\n        <el-form-item label=\"审核状态\">\n          <el-tag v-if=\"viewForm.auditStatus === '0'\" type=\"warning\">待审核</el-tag>\n          <el-tag v-else-if=\"viewForm.auditStatus === '1'\" type=\"success\">审核通过</el-tag>\n          <el-tag v-else-if=\"viewForm.auditStatus === '2'\" type=\"danger\">审核拒绝</el-tag>\n        </el-form-item>\n        <el-form-item label=\"审核备注\" v-if=\"viewForm.auditRemark\">\n          <span>{{ viewForm.auditRemark }}</span>\n        </el-form-item>\n        <el-form-item label=\"审核人\" v-if=\"viewForm.auditBy\">\n          <span>{{ viewForm.auditBy }}</span>\n        </el-form-item>\n        <el-form-item label=\"审核时间\" v-if=\"viewForm.auditTime\">\n          <span>{{ parseTime(viewForm.auditTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\n        </el-form-item>\n        <el-form-item label=\"创建时间\">\n          <span>{{ parseTime(viewForm.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"viewOpen = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 审核对话框 -->\n    <el-dialog title=\"审核报名\" :visible.sync=\"auditOpen\" width=\"500px\" append-to-body>\n      <el-form ref=\"auditForm\" :model=\"auditForm\" :rules=\"auditRules\" label-width=\"80px\">\n        <el-form-item label=\"审核结果\" prop=\"auditStatus\">\n          <el-radio-group v-model=\"auditForm.auditStatus\">\n            <el-radio label=\"1\">审核通过</el-radio>\n            <el-radio label=\"2\">审核拒绝</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        <el-form-item label=\"审核备注\" prop=\"auditRemark\">\n          <el-input v-model=\"auditForm.auditRemark\" type=\"textarea\" placeholder=\"请输入审核备注\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitAudit\">确 定</el-button>\n        <el-button @click=\"auditOpen = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listProjectRegistration, getProjectRegistration, delProjectRegistration, auditProjectRegistration } from \"@/api/miniapp/haitang/projectRegistration\";\n\nexport default {\n  name: \"ProjectRegistration\",\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 天大海棠杯项目报名记录表格数据\n      projectRegistrationList: [],\n      // 是否显示查看详情弹出层\n      viewOpen: false,\n      // 是否显示审核弹出层\n      auditOpen: false,\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        userNickName: null,\n        auditStatus: null,\n        auditBy: null,\n      },\n      // 查看表单参数\n      viewForm: {},\n      // 审核表单参数\n      auditForm: {},\n      // 审核表单校验\n      auditRules: {\n        auditStatus: [\n          { required: true, message: \"审核结果不能为空\", trigger: \"change\" }\n        ],\n      }\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    /** 查询天大海棠杯项目报名记录列表 */\n    getList() {\n      this.loading = true;\n      listProjectRegistration(this.queryParams).then(response => {\n        this.projectRegistrationList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.registrationId)\n      this.multiple = !selection.length\n    },\n    /** 查看按钮操作 */\n    handleView(row) {\n      getProjectRegistration(row.registrationId).then(response => {\n        this.viewForm = response.data;\n        this.viewOpen = true;\n      });\n    },\n    /** 审核按钮操作 */\n    handleAudit(row) {\n      this.auditForm = {\n        registrationId: row.registrationId,\n        auditStatus: null,\n        auditRemark: null\n      };\n      this.auditOpen = true;\n    },\n    /** 提交审核 */\n    submitAudit() {\n      this.$refs[\"auditForm\"].validate(valid => {\n        if (valid) {\n          auditProjectRegistration(this.auditForm).then(response => {\n            this.$modal.msgSuccess(\"审核成功\");\n            this.auditOpen = false;\n            this.getList();\n          });\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const registrationIds = row.registrationId || this.ids;\n      this.$modal.confirm('是否确认删除天大海棠杯项目报名记录编号为\"' + registrationIds + '\"的数据项？').then(function() {\n        return delProjectRegistration(registrationIds);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download('miniapp/haitang/project-registration/export', {\n        ...this.queryParams\n      }, `project-registration_${new Date().getTime()}.xlsx`)\n    },\n    /** 格式化表单数据 */\n    formatFormData(formData) {\n      if (!formData) return '';\n      try {\n        return JSON.stringify(JSON.parse(formData), null, 2);\n      } catch (e) {\n        return formData;\n      }\n    }\n  }\n};\n</script>\n"]}]}