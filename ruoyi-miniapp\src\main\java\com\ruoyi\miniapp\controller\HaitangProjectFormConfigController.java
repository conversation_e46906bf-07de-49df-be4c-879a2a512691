package com.ruoyi.miniapp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.miniapp.domain.HaitangProjectFormConfig;
import com.ruoyi.miniapp.service.IHaitangProjectFormConfigService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 天大海棠杯项目报名表单配置Controller
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
@Api(tags = "创赛路演-项目报名配置")
@RestController
@RequestMapping("/miniapp/haitang/project-config")
public class HaitangProjectFormConfigController extends BaseController
{
    @Autowired
    private IHaitangProjectFormConfigService haitangProjectFormConfigService;

    /**
     * 查询天大海棠杯项目报名表单配置列表
     */
    @ApiOperation("查询项目报名配置列表")
    @PreAuthorize("@ss.hasPermi('miniapp:haitang:project-config:list')")
    @GetMapping("/list")
    public TableDataInfo list(@ApiParam("查询条件") HaitangProjectFormConfig haitangProjectFormConfig)
    {
        startPage();
        List<HaitangProjectFormConfig> list = haitangProjectFormConfigService.selectHaitangProjectFormConfigList(haitangProjectFormConfig);
        return getDataTable(list);
    }

    /**
     * 导出天大海棠杯项目报名表单配置列表
     */
    @ApiOperation("导出项目报名配置列表")
    @PreAuthorize("@ss.hasPermi('miniapp:haitang:project-config:export')")
    @Log(title = "天大海棠杯项目报名表单配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @ApiParam("查询条件") HaitangProjectFormConfig haitangProjectFormConfig)
    {
        List<HaitangProjectFormConfig> list = haitangProjectFormConfigService.selectHaitangProjectFormConfigList(haitangProjectFormConfig);
        ExcelUtil<HaitangProjectFormConfig> util = new ExcelUtil<HaitangProjectFormConfig>(HaitangProjectFormConfig.class);
        util.exportExcel(response, list, "天大海棠杯项目报名表单配置数据");
    }

    /**
     * 获取天大海棠杯项目报名表单配置详细信息
     */
    @ApiOperation("获取项目报名配置详细信息")
    @PreAuthorize("@ss.hasPermi('miniapp:haitang:project-config:query')")
    @GetMapping(value = "/{configId}")
    public AjaxResult getInfo(@ApiParam("配置ID") @PathVariable("configId") Long configId)
    {
        return AjaxResult.success(haitangProjectFormConfigService.selectHaitangProjectFormConfigByConfigId(configId));
    }

    /**
     * 新增天大海棠杯项目报名表单配置
     */
    @ApiOperation("新增项目报名配置")
    @PreAuthorize("@ss.hasPermi('miniapp:haitang:project-config:add')")
    @Log(title = "天大海棠杯项目报名表单配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@ApiParam("配置信息") @RequestBody HaitangProjectFormConfig haitangProjectFormConfig)
    {
        return toAjax(haitangProjectFormConfigService.insertHaitangProjectFormConfig(haitangProjectFormConfig));
    }

    /**
     * 修改天大海棠杯项目报名表单配置
     */
    @ApiOperation("修改项目报名配置")
    @PreAuthorize("@ss.hasPermi('miniapp:haitang:project-config:edit')")
    @Log(title = "天大海棠杯项目报名表单配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@ApiParam("配置信息") @RequestBody HaitangProjectFormConfig haitangProjectFormConfig)
    {
        return toAjax(haitangProjectFormConfigService.updateHaitangProjectFormConfig(haitangProjectFormConfig));
    }

    /**
     * 删除天大海棠杯项目报名表单配置
     */
    @ApiOperation("删除项目报名配置")
    @PreAuthorize("@ss.hasPermi('miniapp:haitang:project-config:remove')")
    @Log(title = "天大海棠杯项目报名表单配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{configIds}")
    public AjaxResult remove(@ApiParam("配置ID数组") @PathVariable Long[] configIds)
    {
        return toAjax(haitangProjectFormConfigService.deleteHaitangProjectFormConfigByConfigIds(configIds));
    }

    /**
     * 启用表单配置
     */
    @ApiOperation("启用表单配置")
    @PreAuthorize("@ss.hasPermi('miniapp:haitang:project-config:edit')")
    @Log(title = "启用项目报名表单配置", businessType = BusinessType.UPDATE)
    @PutMapping("/enable/{configId}")
    public AjaxResult enableConfig(@ApiParam("配置ID") @PathVariable Long configId)
    {
        return toAjax(haitangProjectFormConfigService.enableFormConfig(configId));
    }

    // ==================== 小程序端接口 ====================

    /**
     * 获取启用的表单配置（小程序端）
     */
    @ApiOperation("获取启用的表单配置")
    @GetMapping("/app/getEnabledConfig")
    public AjaxResult getEnabledConfig()
    {
        HaitangProjectFormConfig config = haitangProjectFormConfigService.selectEnabledFormConfig();
        if (config == null) {
            return AjaxResult.error("暂无启用的报名配置");
        }
        return AjaxResult.success(config);
    }
}
