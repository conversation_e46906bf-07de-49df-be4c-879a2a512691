package com.ruoyi.miniapp.mapper;

import java.util.List;
import com.ruoyi.miniapp.domain.HaitangProjectFormConfig;

/**
 * 天大海棠杯项目报名表单配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
public interface HaitangProjectFormConfigMapper 
{
    /**
     * 查询天大海棠杯项目报名表单配置
     * 
     * @param configId 天大海棠杯项目报名表单配置主键
     * @return 天大海棠杯项目报名表单配置
     */
    public HaitangProjectFormConfig selectHaitangProjectFormConfigByConfigId(Long configId);

    /**
     * 查询天大海棠杯项目报名表单配置列表
     * 
     * @param haitangProjectFormConfig 天大海棠杯项目报名表单配置
     * @return 天大海棠杯项目报名表单配置集合
     */
    public List<HaitangProjectFormConfig> selectHaitangProjectFormConfigList(HaitangProjectFormConfig haitangProjectFormConfig);

    /**
     * 新增天大海棠杯项目报名表单配置
     * 
     * @param haitangProjectFormConfig 天大海棠杯项目报名表单配置
     * @return 结果
     */
    public int insertHaitangProjectFormConfig(HaitangProjectFormConfig haitangProjectFormConfig);

    /**
     * 修改天大海棠杯项目报名表单配置
     * 
     * @param haitangProjectFormConfig 天大海棠杯项目报名表单配置
     * @return 结果
     */
    public int updateHaitangProjectFormConfig(HaitangProjectFormConfig haitangProjectFormConfig);

    /**
     * 删除天大海棠杯项目报名表单配置
     * 
     * @param configId 天大海棠杯项目报名表单配置主键
     * @return 结果
     */
    public int deleteHaitangProjectFormConfigByConfigId(Long configId);

    /**
     * 批量删除天大海棠杯项目报名表单配置
     * 
     * @param configIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHaitangProjectFormConfigByConfigIds(Long[] configIds);

    /**
     * 查询启用的表单配置
     * 
     * @return 启用的表单配置
     */
    public HaitangProjectFormConfig selectEnabledFormConfig();

    /**
     * 禁用所有表单配置
     * 
     * @return 结果
     */
    public int disableAllFormConfigs();

    /**
     * 启用指定表单配置
     * 
     * @param configId 配置ID
     * @return 结果
     */
    public int enableFormConfig(Long configId);
}
