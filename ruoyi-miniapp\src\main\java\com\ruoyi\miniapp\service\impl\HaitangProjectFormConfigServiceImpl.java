package com.ruoyi.miniapp.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.miniapp.mapper.HaitangProjectFormConfigMapper;
import com.ruoyi.miniapp.domain.HaitangProjectFormConfig;
import com.ruoyi.miniapp.service.IHaitangProjectFormConfigService;

/**
 * 天大海棠杯项目报名表单配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
@Service
public class HaitangProjectFormConfigServiceImpl implements IHaitangProjectFormConfigService 
{
    @Autowired
    private HaitangProjectFormConfigMapper haitangProjectFormConfigMapper;

    /**
     * 查询天大海棠杯项目报名表单配置
     * 
     * @param configId 天大海棠杯项目报名表单配置主键
     * @return 天大海棠杯项目报名表单配置
     */
    @Override
    public HaitangProjectFormConfig selectHaitangProjectFormConfigByConfigId(Long configId)
    {
        return haitangProjectFormConfigMapper.selectHaitangProjectFormConfigByConfigId(configId);
    }

    /**
     * 查询天大海棠杯项目报名表单配置列表
     * 
     * @param haitangProjectFormConfig 天大海棠杯项目报名表单配置
     * @return 天大海棠杯项目报名表单配置
     */
    @Override
    public List<HaitangProjectFormConfig> selectHaitangProjectFormConfigList(HaitangProjectFormConfig haitangProjectFormConfig)
    {
        return haitangProjectFormConfigMapper.selectHaitangProjectFormConfigList(haitangProjectFormConfig);
    }

    /**
     * 新增天大海棠杯项目报名表单配置
     * 
     * @param haitangProjectFormConfig 天大海棠杯项目报名表单配置
     * @return 结果
     */
    @Override
    public int insertHaitangProjectFormConfig(HaitangProjectFormConfig haitangProjectFormConfig)
    {
        haitangProjectFormConfig.setCreateTime(DateUtils.getNowDate());
        return haitangProjectFormConfigMapper.insertHaitangProjectFormConfig(haitangProjectFormConfig);
    }

    /**
     * 修改天大海棠杯项目报名表单配置
     * 
     * @param haitangProjectFormConfig 天大海棠杯项目报名表单配置
     * @return 结果
     */
    @Override
    public int updateHaitangProjectFormConfig(HaitangProjectFormConfig haitangProjectFormConfig)
    {
        haitangProjectFormConfig.setUpdateTime(DateUtils.getNowDate());
        return haitangProjectFormConfigMapper.updateHaitangProjectFormConfig(haitangProjectFormConfig);
    }

    /**
     * 批量删除天大海棠杯项目报名表单配置
     * 
     * @param configIds 需要删除的天大海棠杯项目报名表单配置主键
     * @return 结果
     */
    @Override
    public int deleteHaitangProjectFormConfigByConfigIds(Long[] configIds)
    {
        return haitangProjectFormConfigMapper.deleteHaitangProjectFormConfigByConfigIds(configIds);
    }

    /**
     * 删除天大海棠杯项目报名表单配置信息
     * 
     * @param configId 天大海棠杯项目报名表单配置主键
     * @return 结果
     */
    @Override
    public int deleteHaitangProjectFormConfigByConfigId(Long configId)
    {
        return haitangProjectFormConfigMapper.deleteHaitangProjectFormConfigByConfigId(configId);
    }

    /**
     * 查询启用的表单配置
     * 
     * @return 启用的表单配置
     */
    @Override
    public HaitangProjectFormConfig selectEnabledFormConfig()
    {
        return haitangProjectFormConfigMapper.selectEnabledFormConfig();
    }

    /**
     * 启用表单配置（同时禁用其他配置）
     * 
     * @param configId 配置ID
     * @return 结果
     */
    @Override
    @Transactional
    public int enableFormConfig(Long configId)
    {
        // 先禁用所有配置
        haitangProjectFormConfigMapper.disableAllFormConfigs();
        // 再启用指定配置
        return haitangProjectFormConfigMapper.enableFormConfig(configId);
    }
}
