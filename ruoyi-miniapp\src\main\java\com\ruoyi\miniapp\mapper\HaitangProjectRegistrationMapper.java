package com.ruoyi.miniapp.mapper;

import java.util.List;
import com.ruoyi.miniapp.domain.HaitangProjectRegistration;

/**
 * 天大海棠杯项目报名记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
public interface HaitangProjectRegistrationMapper 
{
    /**
     * 查询天大海棠杯项目报名记录
     * 
     * @param registrationId 天大海棠杯项目报名记录主键
     * @return 天大海棠杯项目报名记录
     */
    public HaitangProjectRegistration selectHaitangProjectRegistrationByRegistrationId(Long registrationId);

    /**
     * 查询天大海棠杯项目报名记录列表
     * 
     * @param haitangProjectRegistration 天大海棠杯项目报名记录
     * @return 天大海棠杯项目报名记录集合
     */
    public List<HaitangProjectRegistration> selectHaitangProjectRegistrationList(HaitangProjectRegistration haitangProjectRegistration);

    /**
     * 新增天大海棠杯项目报名记录
     * 
     * @param haitangProjectRegistration 天大海棠杯项目报名记录
     * @return 结果
     */
    public int insertHaitangProjectRegistration(HaitangProjectRegistration haitangProjectRegistration);

    /**
     * 修改天大海棠杯项目报名记录
     * 
     * @param haitangProjectRegistration 天大海棠杯项目报名记录
     * @return 结果
     */
    public int updateHaitangProjectRegistration(HaitangProjectRegistration haitangProjectRegistration);

    /**
     * 删除天大海棠杯项目报名记录
     * 
     * @param registrationId 天大海棠杯项目报名记录主键
     * @return 结果
     */
    public int deleteHaitangProjectRegistrationByRegistrationId(Long registrationId);

    /**
     * 批量删除天大海棠杯项目报名记录
     * 
     * @param registrationIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHaitangProjectRegistrationByRegistrationIds(Long[] registrationIds);

    /**
     * 根据用户ID和配置ID查询报名记录
     * 
     * @param userId 用户ID
     * @param configId 配置ID
     * @return 报名记录
     */
    public HaitangProjectRegistration selectByUserIdAndConfigId(Long userId, Long configId);

    /**
     * 审核报名记录
     * 
     * @param haitangProjectRegistration 报名记录
     * @return 结果
     */
    public int auditRegistration(HaitangProjectRegistration haitangProjectRegistration);
}
