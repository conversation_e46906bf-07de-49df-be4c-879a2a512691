{"remainingRequest": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\haitang\\project\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\haitang\\project\\index.vue", "mtime": 1754036951615}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753843467163}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753843480362}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753843467163}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753843484659}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1753781940188}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753843467163}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753843480362}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753843467163}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753843484659}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_project", "require", "_ImageUpload", "_interopRequireDefault", "name", "components", "ImageUpload", "data", "loading", "ids", "multiple", "showSearch", "total", "projectList", "sponsorOpen", "viewOpen", "queryParams", "pageNum", "pageSize", "projectName", "city", "industry", "sponsorForm", "sponsorUnit", "viewForm", "created", "getList", "methods", "_this", "listProject", "then", "response", "rows", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "handleSelectionChange", "selection", "map", "item", "id", "length", "handleSponsorUpload", "_this2", "getSponsorImage", "catch", "error", "cancelSponsor", "submitSponsorForm", "_this3", "updateSponsorImage", "$modal", "msgSuccess", "msgError", "handleView", "row", "handleDelete", "_this4", "confirm", "delProject", "handleExport", "download", "_objectSpread2", "default", "concat", "Date", "getTime"], "sources": ["src/views/miniapp/haitang/project/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"项目名称\" prop=\"projectName\">\r\n        <el-input\r\n          v-model=\"queryParams.projectName\"\r\n          placeholder=\"请输入项目名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"城市\" prop=\"city\">\r\n        <el-input\r\n          v-model=\"queryParams.city\"\r\n          placeholder=\"请输入城市\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"行业\" prop=\"industry\">\r\n        <el-input\r\n          v-model=\"queryParams.industry\"\r\n          placeholder=\"请输入行业\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-picture\"\r\n          size=\"mini\"\r\n          @click=\"handleSponsorUpload\"\r\n          v-hasPermi=\"['miniapp:haitang:project:edit']\"\r\n        >赞助商图片</el-button>\r\n      </el-col>\r\n\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['miniapp:haitang:project:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['miniapp:haitang:project:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"projectList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"项目名称\" align=\"center\" prop=\"projectName\" :show-overflow-tooltip=\"true\"/>\r\n      <el-table-column label=\"团队规模\" align=\"center\" prop=\"teamSize\" />\r\n      <el-table-column label=\"城市\" align=\"center\" prop=\"city\" />\r\n      <el-table-column label=\"赛区\" align=\"center\" prop=\"competitionArea\" />\r\n      <el-table-column label=\"行业\" align=\"center\" prop=\"industry\" />\r\n      <el-table-column label=\"天大校友\" align=\"center\" prop=\"isTjuAlumni\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag :type=\"scope.row.isTjuAlumni ? 'success' : 'info'\">\r\n            {{ scope.row.isTjuAlumni ? '是' : '否' }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"有公司\" align=\"center\" prop=\"hasCompany\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag :type=\"scope.row.hasCompany ? 'success' : 'info'\">\r\n            {{ scope.row.hasCompany ? '是' : '否' }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"公司名称\" align=\"center\" prop=\"companyName\" :show-overflow-tooltip=\"true\"/>\r\n      <el-table-column label=\"公司Logo\" align=\"center\" prop=\"companyLogo\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <el-image\r\n            v-if=\"scope.row.companyLogo\"\r\n            :src=\"scope.row.companyLogo\"\r\n            :preview-src-list=\"[scope.row.companyLogo]\"\r\n            style=\"width: 50px; height: 50px\"\r\n            fit=\"cover\"\r\n          />\r\n          <span v-else>-</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"联系人\" align=\"center\" prop=\"contactName\" />\r\n      <el-table-column label=\"联系电话\" align=\"center\" prop=\"contactPhone\" />\r\n      <el-table-column label=\"报名时间\" align=\"center\" prop=\"registrationTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.registrationTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-view\"\r\n            @click=\"handleView(scope.row)\"\r\n            v-hasPermi=\"['miniapp:haitang:project:query']\"\r\n          >查看</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['miniapp:haitang:project:remove']\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n\r\n\r\n\r\n\r\n    <!-- 赞助商图片上传对话框 -->\r\n    <el-dialog title=\"赞助商图片管理\" :visible.sync=\"sponsorOpen\" width=\"700px\" append-to-body>\r\n      <el-form ref=\"sponsorForm\" :model=\"sponsorForm\" label-width=\"120px\">\r\n        <el-form-item label=\"当前赞助商图片\">\r\n          <div v-if=\"sponsorForm.sponsorUnit && sponsorForm.sponsorUnit.trim() !== ''\" style=\"padding: 10px; background: #f0f9ff; border: 1px solid #b3d8ff; border-radius: 4px; margin-bottom: 10px;\">\r\n            <i class=\"el-icon-success\" style=\"color: #67c23a; margin-right: 5px;\"></i>\r\n            <span style=\"color: #409eff;\">已设置赞助商图片</span>\r\n          </div>\r\n          <div v-else style=\"padding: 10px; background: #fdf6ec; border: 1px solid #f5dab1; border-radius: 4px; margin-bottom: 10px;\">\r\n            <i class=\"el-icon-warning\" style=\"color: #e6a23c; margin-right: 5px;\"></i>\r\n            <span style=\"color: #e6a23c;\">暂未设置赞助商图片</span>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"上传新图片\">\r\n          <image-upload v-model=\"sponsorForm.sponsorUnit\" :limit=\"1\"/>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitSponsorForm\">确 定</el-button>\r\n        <el-button @click=\"cancelSponsor\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n\r\n\r\n    <!-- 查看详情对话框 -->\r\n    <el-dialog title=\"项目报名详情\" :visible.sync=\"viewOpen\" width=\"800px\" append-to-body>\r\n      <el-descriptions :column=\"2\" border>\r\n        <el-descriptions-item label=\"项目名称\">{{ viewForm.projectName }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"团队规模\">{{ viewForm.teamSize }}人</el-descriptions-item>\r\n        <el-descriptions-item label=\"城市\">{{ viewForm.city }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"赛区\">{{ viewForm.competitionArea }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"行业\">{{ viewForm.industry }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"天大校友\">{{ viewForm.isTjuAlumni ? '是' : '否' }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"项目描述\" :span=\"2\">{{ viewForm.projectDescription }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"是否有公司\">{{ viewForm.hasCompany ? '是' : '否' }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"公司名称\" v-if=\"viewForm.hasCompany\">{{ viewForm.companyName }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"去年营收\" v-if=\"viewForm.hasCompany\">{{ viewForm.lastYearRevenue }}万元</el-descriptions-item>\r\n        <el-descriptions-item label=\"项目估值\" v-if=\"viewForm.hasCompany\">{{ viewForm.projectValuation }}万元</el-descriptions-item>\r\n        <el-descriptions-item label=\"最新融资轮次\" v-if=\"viewForm.hasCompany\">{{ viewForm.latestFundingRound }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"投资机构\" v-if=\"viewForm.hasCompany\">{{ viewForm.investmentInstitution }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"公司logo\" v-if=\"viewForm.hasCompany && viewForm.companyLogo\">\r\n          <el-image\r\n            style=\"width: 100px; height: 60px\"\r\n            :src=\"viewForm.companyLogo\"\r\n            :preview-src-list=\"[viewForm.companyLogo]\"\r\n            fit=\"cover\">\r\n          </el-image>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"项目BP\" v-if=\"viewForm.projectBp\">\r\n          <el-link type=\"primary\" :href=\"viewForm.projectBp\" target=\"_blank\" :underline=\"false\">\r\n            <i class=\"el-icon-download\"></i> 下载项目BP\r\n          </el-link>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"推荐人\">{{ viewForm.recommender }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"赞助单位\" v-if=\"viewForm.sponsorUnit\">\r\n          <el-image\r\n            style=\"width: 120px; height: 60px\"\r\n            :src=\"viewForm.sponsorUnit\"\r\n            :preview-src-list=\"[viewForm.sponsorUnit]\"\r\n            fit=\"contain\">\r\n          </el-image>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"联系人姓名\">{{ viewForm.contactName }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"联系人电话\">{{ viewForm.contactPhone }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"联系人微信\">{{ viewForm.contactWechat }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"联系人职位\">{{ viewForm.contactPosition }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"报名时间\">{{ parseTime(viewForm.registrationTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>\r\n      </el-descriptions>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"viewOpen = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listProject, delProject, updateSponsorImage, getSponsorImage } from \"@/api/miniapp/haitang/project\";\r\nimport ImageUpload from \"@/components/ImageUpload\";\r\n\r\nexport default {\r\n  name: \"Project\",\r\n  components: {\r\n    ImageUpload\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 项目报名表格数据\r\n      projectList: [],\r\n      // 是否显示赞助商图片弹出层\r\n      sponsorOpen: false,\r\n      // 是否显示查看详情弹出层\r\n      viewOpen: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        projectName: null,\r\n        city: null,\r\n        industry: null\r\n      },\r\n      // 赞助商表单参数\r\n      sponsorForm: {\r\n        sponsorUnit: null\r\n      },\r\n      // 查看详情表单参数\r\n      viewForm: {}\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询项目报名列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listProject(this.queryParams).then(response => {\r\n        this.projectList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.multiple = !selection.length\r\n    },\r\n\r\n    /** 赞助商图片上传按钮操作 */\r\n    handleSponsorUpload() {\r\n      getSponsorImage().then(response => {\r\n        this.sponsorForm.sponsorUnit = response.data || '';\r\n        this.sponsorOpen = true;\r\n      }).catch(error => {\r\n        this.sponsorForm.sponsorUnit = '';\r\n        this.sponsorOpen = true;\r\n      });\r\n    },\r\n    /** 取消赞助商图片上传 */\r\n    cancelSponsor() {\r\n      this.sponsorOpen = false;\r\n      // 不要清空数据，保持原有数据\r\n      // this.sponsorForm.sponsorUnit = null;\r\n    },\r\n    /** 提交赞助商图片 */\r\n    submitSponsorForm() {\r\n      updateSponsorImage(this.sponsorForm.sponsorUnit).then(response => {\r\n        this.$modal.msgSuccess(\"赞助商图片更新成功\");\r\n        this.sponsorOpen = false;\r\n      }).catch(error => {\r\n        this.$modal.msgError(\"更新赞助商图片失败\");\r\n      });\r\n    },\r\n    /** 查看按钮操作 */\r\n    handleView(row) {\r\n      this.viewForm = row;\r\n      this.viewOpen = true;\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$modal.confirm('是否确认删除项目报名编号为\"' + ids + '\"的数据项？').then(function() {\r\n        return delProject(ids);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('miniapp/haitang/project/export', {\r\n        ...this.queryParams\r\n      }, `project_${new Date().getTime()}.xlsx`)\r\n    }\r\n  }\r\n};\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;AA2NA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,YAAA,GAAAC,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,IAAA;EACAC,UAAA;IACAC,WAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,WAAA;MACA;MACAC,WAAA;MACA;MACAC,QAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,WAAA;QACAC,IAAA;QACAC,QAAA;MACA;MACA;MACAC,WAAA;QACAC,WAAA;MACA;MACA;MACAC,QAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,eACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAApB,OAAA;MACA,IAAAqB,oBAAA,OAAAb,WAAA,EAAAc,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAf,WAAA,GAAAkB,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAAhB,KAAA,GAAAmB,QAAA,CAAAnB,KAAA;QACAgB,KAAA,CAAApB,OAAA;MACA;IACA;IAEA,aACAyB,WAAA,WAAAA,YAAA;MACA,KAAAjB,WAAA,CAAAC,OAAA;MACA,KAAAS,OAAA;IACA;IACA,aACAQ,UAAA,WAAAA,WAAA;MACA,KAAAC,SAAA;MACA,KAAAF,WAAA;IACA;IACA;IACAG,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA5B,GAAA,GAAA4B,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,EAAA;MAAA;MACA,KAAA9B,QAAA,IAAA2B,SAAA,CAAAI,MAAA;IACA;IAEA,kBACAC,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,wBAAA,IAAAd,IAAA,WAAAC,QAAA;QACAY,MAAA,CAAArB,WAAA,CAAAC,WAAA,GAAAQ,QAAA,CAAAxB,IAAA;QACAoC,MAAA,CAAA7B,WAAA;MACA,GAAA+B,KAAA,WAAAC,KAAA;QACAH,MAAA,CAAArB,WAAA,CAAAC,WAAA;QACAoB,MAAA,CAAA7B,WAAA;MACA;IACA;IACA,gBACAiC,aAAA,WAAAA,cAAA;MACA,KAAAjC,WAAA;MACA;MACA;IACA;IACA,cACAkC,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,2BAAA,OAAA5B,WAAA,CAAAC,WAAA,EAAAO,IAAA,WAAAC,QAAA;QACAkB,MAAA,CAAAE,MAAA,CAAAC,UAAA;QACAH,MAAA,CAAAnC,WAAA;MACA,GAAA+B,KAAA,WAAAC,KAAA;QACAG,MAAA,CAAAE,MAAA,CAAAE,QAAA;MACA;IACA;IACA,aACAC,UAAA,WAAAA,WAAAC,GAAA;MACA,KAAA/B,QAAA,GAAA+B,GAAA;MACA,KAAAxC,QAAA;IACA;IACA,aACAyC,YAAA,WAAAA,aAAAD,GAAA;MAAA,IAAAE,MAAA;MACA,IAAAhD,GAAA,GAAA8C,GAAA,CAAAf,EAAA,SAAA/B,GAAA;MACA,KAAA0C,MAAA,CAAAO,OAAA,oBAAAjD,GAAA,aAAAqB,IAAA;QACA,WAAA6B,mBAAA,EAAAlD,GAAA;MACA,GAAAqB,IAAA;QACA2B,MAAA,CAAA/B,OAAA;QACA+B,MAAA,CAAAN,MAAA,CAAAC,UAAA;MACA,GAAAP,KAAA;IACA;IACA,aACAe,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,uCAAAC,cAAA,CAAAC,OAAA,MACA,KAAA/C,WAAA,cAAAgD,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;EACA;AACA", "ignoreList": []}]}