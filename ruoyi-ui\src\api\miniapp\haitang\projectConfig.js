import request from '@/utils/request'

// 查询项目报名配置列表
export function listProjectConfig(query) {
  return request({
    url: '/miniapp/haitang/project-config/list',
    method: 'get',
    params: query
  })
}

// 查询项目报名配置详细
export function getProjectConfig(configId) {
  return request({
    url: '/miniapp/haitang/project-config/' + configId,
    method: 'get'
  })
}

// 新增项目报名配置
export function addProjectConfig(data) {
  return request({
    url: '/miniapp/haitang/project-config',
    method: 'post',
    data: data
  })
}

// 修改项目报名配置
export function updateProjectConfig(data) {
  return request({
    url: '/miniapp/haitang/project-config',
    method: 'put',
    data: data
  })
}

// 删除项目报名配置
export function delProjectConfig(configId) {
  return request({
    url: '/miniapp/haitang/project-config/' + configId,
    method: 'delete'
  })
}

// 启用表单配置
export function enableProjectConfig(configId) {
  return request({
    url: '/miniapp/haitang/project-config/enable/' + configId,
    method: 'put'
  })
}

// 获取启用的表单配置（小程序端）
export function getEnabledProjectConfig() {
  return request({
    url: '/miniapp/haitang/project-config/app/getEnabledConfig',
    method: 'get'
  })
}
