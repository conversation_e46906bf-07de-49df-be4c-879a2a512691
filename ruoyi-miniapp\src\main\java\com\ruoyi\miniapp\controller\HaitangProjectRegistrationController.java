package com.ruoyi.miniapp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.miniapp.domain.HaitangProjectRegistration;
import com.ruoyi.miniapp.service.IHaitangProjectRegistrationService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 天大海棠杯项目报名记录Controller
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
@Api(tags = "创赛路演-项目报名记录")
@RestController
@RequestMapping("/miniapp/haitang/project-registration")
public class HaitangProjectRegistrationController extends BaseController
{
    @Autowired
    private IHaitangProjectRegistrationService haitangProjectRegistrationService;

    /**
     * 查询天大海棠杯项目报名记录列表
     */
    @ApiOperation("查询项目报名记录列表")
    @PreAuthorize("@ss.hasPermi('miniapp:haitang:project-registration:list')")
    @GetMapping("/list")
    public TableDataInfo list(@ApiParam("查询条件") HaitangProjectRegistration haitangProjectRegistration)
    {
        startPage();
        List<HaitangProjectRegistration> list = haitangProjectRegistrationService.selectHaitangProjectRegistrationList(haitangProjectRegistration);
        return getDataTable(list);
    }

    /**
     * 导出天大海棠杯项目报名记录列表
     */
    @ApiOperation("导出项目报名记录列表")
    @PreAuthorize("@ss.hasPermi('miniapp:haitang:project-registration:export')")
    @Log(title = "天大海棠杯项目报名记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @ApiParam("查询条件") HaitangProjectRegistration haitangProjectRegistration)
    {
        List<HaitangProjectRegistration> list = haitangProjectRegistrationService.selectHaitangProjectRegistrationList(haitangProjectRegistration);
        ExcelUtil<HaitangProjectRegistration> util = new ExcelUtil<HaitangProjectRegistration>(HaitangProjectRegistration.class);
        util.exportExcel(response, list, "天大海棠杯项目报名记录数据");
    }

    /**
     * 获取天大海棠杯项目报名记录详细信息
     */
    @ApiOperation("获取项目报名记录详细信息")
    @PreAuthorize("@ss.hasPermi('miniapp:haitang:project-registration:query')")
    @GetMapping(value = "/{registrationId}")
    public AjaxResult getInfo(@ApiParam("报名ID") @PathVariable("registrationId") Long registrationId)
    {
        return AjaxResult.success(haitangProjectRegistrationService.selectHaitangProjectRegistrationByRegistrationId(registrationId));
    }

    /**
     * 新增天大海棠杯项目报名记录
     */
    @ApiOperation("新增项目报名记录")
    @PreAuthorize("@ss.hasPermi('miniapp:haitang:project-registration:add')")
    @Log(title = "天大海棠杯项目报名记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@ApiParam("报名记录信息") @RequestBody HaitangProjectRegistration haitangProjectRegistration)
    {
        return toAjax(haitangProjectRegistrationService.insertHaitangProjectRegistration(haitangProjectRegistration));
    }

    /**
     * 修改天大海棠杯项目报名记录
     */
    @ApiOperation("修改项目报名记录")
    @PreAuthorize("@ss.hasPermi('miniapp:haitang:project-registration:edit')")
    @Log(title = "天大海棠杯项目报名记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@ApiParam("报名记录信息") @RequestBody HaitangProjectRegistration haitangProjectRegistration)
    {
        return toAjax(haitangProjectRegistrationService.updateHaitangProjectRegistration(haitangProjectRegistration));
    }

    /**
     * 删除天大海棠杯项目报名记录
     */
    @ApiOperation("删除项目报名记录")
    @PreAuthorize("@ss.hasPermi('miniapp:haitang:project-registration:remove')")
    @Log(title = "天大海棠杯项目报名记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{registrationIds}")
    public AjaxResult remove(@ApiParam("报名ID数组") @PathVariable Long[] registrationIds)
    {
        return toAjax(haitangProjectRegistrationService.deleteHaitangProjectRegistrationByRegistrationIds(registrationIds));
    }

    /**
     * 审核项目报名记录
     */
    @ApiOperation("审核项目报名记录")
    @PreAuthorize("@ss.hasPermi('miniapp:haitang:project-registration:audit')")
    @Log(title = "审核项目报名记录", businessType = BusinessType.UPDATE)
    @PutMapping("/audit")
    public AjaxResult audit(@ApiParam("审核信息") @RequestBody HaitangProjectRegistration haitangProjectRegistration)
    {
        return toAjax(haitangProjectRegistrationService.auditRegistration(haitangProjectRegistration));
    }

    // ==================== 小程序端接口 ====================

    /**
     * 提交项目报名（小程序端）
     */
    @ApiOperation("提交项目报名")
    @PostMapping("/app/submit")
    public AjaxResult submitRegistration(@ApiParam("报名信息") @RequestBody HaitangProjectRegistration haitangProjectRegistration)
    {
        try {
            return toAjax(haitangProjectRegistrationService.submitRegistration(haitangProjectRegistration));
        } catch (RuntimeException e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 查询用户报名记录（小程序端）
     */
    @ApiOperation("查询用户报名记录")
    @GetMapping("/app/getUserRegistration/{userId}/{configId}")
    public AjaxResult getUserRegistration(@ApiParam("用户ID") @PathVariable Long userId, 
                                        @ApiParam("配置ID") @PathVariable Long configId)
    {
        HaitangProjectRegistration registration = haitangProjectRegistrationService.selectByUserIdAndConfigId(userId, configId);
        return AjaxResult.success(registration);
    }
}
