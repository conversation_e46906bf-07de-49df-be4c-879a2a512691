import request from '@/utils/request'

// 查询项目报名记录列表
export function listProjectRegistration(query) {
  return request({
    url: '/miniapp/haitang/project-registration/list',
    method: 'get',
    params: query
  })
}

// 查询项目报名记录详细
export function getProjectRegistration(registrationId) {
  return request({
    url: '/miniapp/haitang/project-registration/' + registrationId,
    method: 'get'
  })
}

// 新增项目报名记录
export function addProjectRegistration(data) {
  return request({
    url: '/miniapp/haitang/project-registration',
    method: 'post',
    data: data
  })
}

// 修改项目报名记录
export function updateProjectRegistration(data) {
  return request({
    url: '/miniapp/haitang/project-registration',
    method: 'put',
    data: data
  })
}

// 删除项目报名记录
export function delProjectRegistration(registrationId) {
  return request({
    url: '/miniapp/haitang/project-registration/' + registrationId,
    method: 'delete'
  })
}

// 审核项目报名记录
export function auditProjectRegistration(data) {
  return request({
    url: '/miniapp/haitang/project-registration/audit',
    method: 'put',
    data: data
  })
}

// 提交项目报名（小程序端）
export function submitProjectRegistration(data) {
  return request({
    url: '/miniapp/haitang/project-registration/app/submit',
    method: 'post',
    data: data
  })
}

// 查询用户报名记录（小程序端）
export function getUserProjectRegistration(userId, configId) {
  return request({
    url: '/miniapp/haitang/project-registration/app/getUserRegistration/' + userId + '/' + configId,
    method: 'get'
  })
}
