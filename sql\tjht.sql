/*
 Navicat Premium Dump SQL

 Source Server         : docker_mysql
 Source Server Type    : MySQL
 Source Server Version : 80042 (8.0.42)
 Source Host           : localhost:3306
 Source Schema         : tjht

 Target Server Type    : MySQL
 Target Server Version : 80042 (8.0.42)
 File Encoding         : 65001

 Date: 04/07/2025 11:02:25
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for mini_activity
-- ----------------------------
DROP TABLE IF EXISTS `mini_activity`;
CREATE TABLE `mini_activity`  (
  `activity_id` bigint NOT NULL AUTO_INCREMENT COMMENT '活动ID',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '活动标题',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL COMMENT '活动描述',
  `cover_image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '封面图片',
  `article_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '公众号文章链接',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序（数字越小越靠前）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`activity_id`) USING BTREE,
  INDEX `idx_sort_order`(`sort_order` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '精彩活动表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of mini_activity
-- ----------------------------

-- ----------------------------
-- Table structure for mini_banner
-- ----------------------------
DROP TABLE IF EXISTS `mini_banner`;
CREATE TABLE `mini_banner`  (
  `banner_id` bigint NOT NULL AUTO_INCREMENT COMMENT '轮播图ID',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '轮播图标题',
  `image_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '图片地址',
  `link_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '跳转链接',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序（数字越小越靠前）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`banner_id`) USING BTREE,
  INDEX `idx_sort_order`(`sort_order` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '轮播图表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of mini_banner
-- ----------------------------

-- ----------------------------
-- Table structure for mini_barrage
-- ----------------------------
DROP TABLE IF EXISTS `mini_barrage`;
CREATE TABLE `mini_barrage`  (
  `barrage_id` bigint NOT NULL AUTO_INCREMENT COMMENT '弹幕ID',
  `user_id` bigint NOT NULL COMMENT '发布用户ID',
  `user_nick_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '发布用户昵称（冗余字段）',
  `user_avatar_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '发布用户头像（冗余字段）',
  `content` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '弹幕内容',
  `audit_status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '审核状态（0待审核 1审核通过 2审核拒绝）',
  `audit_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '审核人',
  `audit_time` datetime NULL DEFAULT NULL COMMENT '审核时间',
  `audit_remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '审核备注',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`barrage_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_audit_status`(`audit_status` ASC) USING BTREE,
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '弹幕表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of mini_barrage
-- ----------------------------

-- ----------------------------
-- Table structure for mini_demand
-- ----------------------------
DROP TABLE IF EXISTS `mini_demand`;
CREATE TABLE `mini_demand`  (
  `demand_id` bigint NOT NULL AUTO_INCREMENT COMMENT '需求ID',
  `category_id` bigint NOT NULL COMMENT '分类ID',
  `user_id` bigint NOT NULL COMMENT '发布用户ID',
  `demand_title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '需求标题',
  `demand_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '需求类型',
  `demand_desc` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL COMMENT '需求描述',
  `contact_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '联系人姓名',
  `contact_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '联系人手机',
  `demand_status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '需求状态（0已发布 1已对接 2已下架）',
  `is_top` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '是否置顶（0否 1是）',
  `view_count` int NULL DEFAULT 0 COMMENT '浏览次数',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`demand_id`) USING BTREE,
  INDEX `idx_category_id`(`category_id` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_demand_status`(`demand_status` ASC) USING BTREE,
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE,
  INDEX `idx_is_top`(`is_top` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '需求信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of mini_demand
-- ----------------------------

-- ----------------------------
-- Table structure for mini_demand_category
-- ----------------------------
DROP TABLE IF EXISTS `mini_demand_category`;
CREATE TABLE `mini_demand_category`  (
  `category_id` bigint NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `category_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '分类名称',
  `category_desc` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL COMMENT '分类详细介绍',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`category_id`) USING BTREE,
  INDEX `idx_sort_order`(`sort_order` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '需求分类表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of mini_demand_category
-- ----------------------------

-- ----------------------------
-- Table structure for mini_event
-- ----------------------------
DROP TABLE IF EXISTS `mini_event`;
CREATE TABLE `mini_event` (
  `event_id` bigint NOT NULL AUTO_INCREMENT COMMENT '活动ID',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '活动标题',
  `cover_image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '活动封面图',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci COMMENT '活动描述',
  `location` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT '' COMMENT '活动地点',
  `start_time` datetime NOT NULL COMMENT '活动开始时间',
  `end_time` datetime NOT NULL COMMENT '活动结束时间',
  `registration_deadline` datetime DEFAULT NULL COMMENT '报名截止时间',
  `form_fields` json DEFAULT NULL COMMENT '报名表单字段配置（JSON格式）',
  `sort_order` int DEFAULT '0' COMMENT '排序（数字越小越靠前）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`event_id`) USING BTREE,
  KEY `idx_sort_order` (`sort_order`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_start_time` (`start_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci ROW_FORMAT=DYNAMIC COMMENT='活动报名表';

-- ----------------------------
-- Records of mini_event
-- ----------------------------

-- ----------------------------
-- Table structure for mini_event_registration
-- ----------------------------
DROP TABLE IF EXISTS `mini_event_registration`;
CREATE TABLE `mini_event_registration`  (
  `registration_id` bigint NOT NULL AUTO_INCREMENT COMMENT '报名ID',
  `event_id` bigint NOT NULL COMMENT '活动ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `form_data` json NULL COMMENT '报名表单数据（JSON格式）',
  `registration_time` datetime NOT NULL COMMENT '报名时间',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '报名状态（0已报名 1已取消）',
  PRIMARY KEY (`registration_id`) USING BTREE,
  UNIQUE INDEX `uk_event_user`(`event_id` ASC, `user_id` ASC) USING BTREE,
  INDEX `idx_event_id`(`event_id` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_registration_time`(`registration_time` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '用户报名记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of mini_event_registration
-- ----------------------------

-- ----------------------------
-- Table structure for mini_job
-- ----------------------------
DROP TABLE IF EXISTS `mini_job`;
CREATE TABLE `mini_job`  (
  `job_id` bigint NOT NULL AUTO_INCREMENT COMMENT '职位ID',
  `job_title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '职位名称',
  `company_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '公司名称',
  `company_scale` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '公司规模',
  `salary_range` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '薪资范围',
  `job_tags` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '职位标签（多个用逗号分隔）',
  `work_location` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '工作地点',
  `job_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL COMMENT '职位描述',
  `requirements` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL COMMENT '任职要求',
  `contact_info` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '联系方式',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序（数字越小越靠前）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`job_id`) USING BTREE,
  INDEX `idx_sort_order`(`sort_order` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '招聘职位表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of mini_job
-- ----------------------------

-- ----------------------------
-- Table structure for mini_notice
-- ----------------------------
DROP TABLE IF EXISTS `mini_notice`;
CREATE TABLE `mini_notice`  (
  `notice_id` bigint NOT NULL AUTO_INCREMENT COMMENT '通知ID',
  `content` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '通知内容',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序（数字越小越靠前）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`notice_id`) USING BTREE,
  INDEX `idx_sort_order`(`sort_order` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '通知表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of mini_notice
-- ----------------------------

-- ----------------------------
-- Table structure for mini_page_content
-- ----------------------------
DROP TABLE IF EXISTS `mini_page_content`;
CREATE TABLE `mini_page_content`  (
  `content_id` bigint NOT NULL AUTO_INCREMENT COMMENT '内容ID',
  `page_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '页面编码',
  `page_title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '页面标题',
  `page_content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL COMMENT '页面内容（支持富文本）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`content_id`) USING BTREE,
  UNIQUE INDEX `uk_page_code`(`page_code` ASC) USING BTREE,
  INDEX `idx_page_code`(`page_code` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '页面内容管理表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of mini_page_content
-- ----------------------------

-- ----------------------------
-- Table structure for mini_tag
-- ----------------------------
DROP TABLE IF EXISTS `mini_tag`;
CREATE TABLE `mini_tag`  (
  `tag_id` bigint NOT NULL AUTO_INCREMENT COMMENT '标签ID',
  `category_id` bigint NOT NULL COMMENT '分类ID',
  `tag_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '标签名称',
  `tag_color` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '#409EFF' COMMENT '标签颜色',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`tag_id`) USING BTREE,
  INDEX `idx_category_id`(`category_id` ASC) USING BTREE,
  INDEX `idx_sort_order`(`sort_order` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 30 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '标签表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of mini_tag
-- ----------------------------
INSERT INTO `mini_tag` VALUES (1, 1, 'CEO/创始人', '#409EFF', 1, '0', 'admin', '2025-07-04 09:08:16', '', NULL, '');
INSERT INTO `mini_tag` VALUES (2, 1, 'CTO/技术总监', '#409EFF', 2, '0', 'admin', '2025-07-04 09:08:16', '', NULL, '');
INSERT INTO `mini_tag` VALUES (3, 1, '产品经理', '#409EFF', 3, '0', 'admin', '2025-07-04 09:08:16', '', NULL, '');
INSERT INTO `mini_tag` VALUES (4, 1, '技术专家', '#409EFF', 4, '0', 'admin', '2025-07-04 09:08:16', '', NULL, '');
INSERT INTO `mini_tag` VALUES (5, 1, '销售总监', '#409EFF', 5, '0', 'admin', '2025-07-04 09:08:16', '', NULL, '');
INSERT INTO `mini_tag` VALUES (6, 1, '财务总监', '#409EFF', 6, '0', 'admin', '2025-07-04 09:08:16', '', NULL, '');
INSERT INTO `mini_tag` VALUES (7, 1, 'HR总监', '#409EFF', 7, '0', 'admin', '2025-07-04 09:08:16', '', NULL, '');
INSERT INTO `mini_tag` VALUES (8, 1, '运营总监', '#409EFF', 8, '0', 'admin', '2025-07-04 09:08:16', '', NULL, '');
INSERT INTO `mini_tag` VALUES (9, 2, '人工智能', '#67C23A', 1, '0', 'admin', '2025-07-04 09:08:16', '', NULL, '');
INSERT INTO `mini_tag` VALUES (10, 2, '大数据', '#67C23A', 2, '0', 'admin', '2025-07-04 09:08:16', '', NULL, '');
INSERT INTO `mini_tag` VALUES (11, 2, '区块链', '#67C23A', 3, '0', 'admin', '2025-07-04 09:08:16', '', NULL, '');
INSERT INTO `mini_tag` VALUES (12, 2, '物联网', '#67C23A', 4, '0', 'admin', '2025-07-04 09:08:16', '', NULL, '');
INSERT INTO `mini_tag` VALUES (13, 2, '云计算', '#67C23A', 5, '0', 'admin', '2025-07-04 09:08:16', '', NULL, '');
INSERT INTO `mini_tag` VALUES (14, 2, '移动互联网', '#67C23A', 6, '0', 'admin', '2025-07-04 09:08:16', '', NULL, '');
INSERT INTO `mini_tag` VALUES (15, 2, '电子商务', '#67C23A', 7, '0', 'admin', '2025-07-04 09:08:16', '', NULL, '');
INSERT INTO `mini_tag` VALUES (16, 3, 'PMP认证', '#E6A23C', 1, '0', 'admin', '2025-07-04 09:08:16', '', NULL, '');
INSERT INTO `mini_tag` VALUES (17, 3, 'CPA执业会计师', '#E6A23C', 2, '0', 'admin', '2025-07-04 09:08:16', '', NULL, '');
INSERT INTO `mini_tag` VALUES (18, 3, 'MBA学历', '#E6A23C', 3, '0', 'admin', '2025-07-04 09:08:16', '', NULL, '');
INSERT INTO `mini_tag` VALUES (19, 3, '律师资格证', '#E6A23C', 4, '0', 'admin', '2025-07-04 09:08:16', '', NULL, '');
INSERT INTO `mini_tag` VALUES (20, 3, '注册会计师', '#E6A23C', 5, '0', 'admin', '2025-07-04 09:08:16', '', NULL, '');
INSERT INTO `mini_tag` VALUES (21, 3, '税务师', '#E6A23C', 6, '0', 'admin', '2025-07-04 09:08:16', '', NULL, '');
INSERT INTO `mini_tag` VALUES (22, 3, '审计师', '#E6A23C', 7, '0', 'admin', '2025-07-04 09:08:16', '', NULL, '');
INSERT INTO `mini_tag` VALUES (23, 4, '高管', '#F56C6C', 1, '0', 'admin', '2025-07-04 09:08:16', '', NULL, '');
INSERT INTO `mini_tag` VALUES (24, 4, '创业者', '#F56C6C', 2, '0', 'admin', '2025-07-04 09:08:16', '', NULL, '');
INSERT INTO `mini_tag` VALUES (25, 4, '投资人', '#F56C6C', 3, '0', 'admin', '2025-07-04 09:08:16', '', NULL, '');
INSERT INTO `mini_tag` VALUES (26, 4, '技术专家', '#F56C6C', 4, '0', 'admin', '2025-07-04 09:08:16', '', NULL, '');
INSERT INTO `mini_tag` VALUES (27, 4, '创业导师', '#F56C6C', 5, '0', 'admin', '2025-07-04 09:08:16', '', NULL, '');
INSERT INTO `mini_tag` VALUES (28, 4, '企业家', '#F56C6C', 6, '0', 'admin', '2025-07-04 09:08:16', '', NULL, '');
INSERT INTO `mini_tag` VALUES (29, 4, '专家学者', '#F56C6C', 7, '0', 'admin', '2025-07-04 09:08:16', '', NULL, '');

-- ----------------------------
-- Table structure for mini_tag_category
-- ----------------------------
DROP TABLE IF EXISTS `mini_tag_category`;
CREATE TABLE `mini_tag_category`  (
  `category_id` bigint NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `category_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '分类名称',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`category_id`) USING BTREE,
  INDEX `idx_sort_order`(`sort_order` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '标签分类表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of mini_tag_category
-- ----------------------------
INSERT INTO `mini_tag_category` VALUES (1, '职位标签', 1, '0', 'admin', '2025-07-04 09:07:20', '', NULL, '职位相关标签');
INSERT INTO `mini_tag_category` VALUES (2, '行业标签', 2, '0', 'admin', '2025-07-04 09:07:20', '', NULL, '行业相关标签');
INSERT INTO `mini_tag_category` VALUES (3, '资质标签', 3, '0', 'admin', '2025-07-04 09:07:20', '', NULL, '资质相关标签');
INSERT INTO `mini_tag_category` VALUES (4, '身份标签', 4, '0', 'admin', '2025-07-04 09:07:20', '', NULL, '身份相关标签');

-- ----------------------------
-- Table structure for mini_tech_star
-- ----------------------------
DROP TABLE IF EXISTS `mini_tech_star`;
CREATE TABLE `mini_tech_star`  (
  `star_id` bigint NOT NULL AUTO_INCREMENT COMMENT '科技之星ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '姓名',
  `photo_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '照片地址',
  `company_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '企业名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL COMMENT '简介',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序（数字越小越靠前）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`star_id`) USING BTREE,
  INDEX `idx_sort_order`(`sort_order` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '科技之星表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of mini_tech_star
-- ----------------------------

-- ----------------------------
-- Table structure for mini_user
-- ----------------------------
DROP TABLE IF EXISTS `mini_user`;
CREATE TABLE `mini_user`  (
  `user_id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `openid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '微信OpenID',
  `unionid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '微信UnionID',
  `session_key` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '会话密钥',
  `nick_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '微信昵称',
  `avatar_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '微信头像',
  `real_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '真实姓名',
  `gender` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '性别（0未知 1男 2女）',
  `birth_date` date NULL DEFAULT NULL COMMENT '出生年月日',
  `mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '手机号码',
  `basic_info_completed` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '基本信息是否完成（0否 1是）',
  `graduate_school` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '毕业院校',
  `graduate_college` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '毕业学院',
  `graduate_year` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '毕业年份',
  `education_completed` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '教育背景是否完成（0否 1是）',
  `company_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '所属企业名称',
  `position` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '职位',
  `industry` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '所属行业',
  `career_completed` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '职业信息是否完成（0否 1是）',
  `profile_image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '形象照',
  `self_intro` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '自我介绍',
  `display_completed` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '个人展示是否完成（0否 1是）',
  `tags_completed` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '标签信息是否完成（0否 1是）',
  `login_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT 'wechat' COMMENT '主要登录方式（wechat:微信 mobile:手机号）',
  `total_points` int NULL DEFAULT 0 COMMENT '总积分',
  `profile_completion_rate` int NULL DEFAULT 0 COMMENT '资料完成度百分比',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '账号状态（0正常 1禁用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '删除标志（0存在 2删除）',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `last_login_time` datetime NULL DEFAULT NULL COMMENT '最后登录时间',
  PRIMARY KEY (`user_id`) USING BTREE,
  UNIQUE INDEX `uk_openid`(`openid` ASC) USING BTREE,
  INDEX `idx_mobile`(`mobile` ASC) USING BTREE,
  INDEX `idx_total_points`(`total_points` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '小程序用户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of mini_user
-- ----------------------------

-- ----------------------------
-- Table structure for mini_user_points
-- ----------------------------
DROP TABLE IF EXISTS `mini_user_points`;
CREATE TABLE `mini_user_points`  (
  `point_id` bigint NOT NULL AUTO_INCREMENT COMMENT '积分记录ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `point_change` int NOT NULL COMMENT '积分变化（正数为获得，负数为消费）',
  `point_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '积分类型（earn:获得 spend:消费）',
  `point_source` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '积分来源/用途',
  `description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '积分说明',
  `before_points` int NULL DEFAULT 0 COMMENT '变化前积分',
  `after_points` int NULL DEFAULT 0 COMMENT '变化后积分',
  `related_id` bigint NULL DEFAULT NULL COMMENT '关联ID（如兑换商品ID等）',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`point_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_point_type`(`point_type` ASC) USING BTREE,
  INDEX `idx_point_source`(`point_source` ASC) USING BTREE,
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '积分记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of mini_user_points
-- ----------------------------

-- ----------------------------
-- Table structure for mini_user_tag
-- ----------------------------
DROP TABLE IF EXISTS `mini_user_tag`;
CREATE TABLE `mini_user_tag`  (
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `tag_id` bigint NOT NULL COMMENT '标签ID',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`user_id`, `tag_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_tag_id`(`tag_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '用户标签关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of mini_user_tag
-- ----------------------------

-- ----------------------------
-- Table structure for haitang_project_form_config
-- ----------------------------
DROP TABLE IF EXISTS `haitang_project_form_config`;
CREATE TABLE `haitang_project_form_config`  (
  `config_id` bigint NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '配置名称',
  `config_description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '配置描述',
  `form_config` json NULL COMMENT '表单配置（JSON格式）',
  `is_enabled` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '是否启用（0否 1是）',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序（数字越小越靠前）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`config_id`) USING BTREE,
  INDEX `idx_is_enabled`(`is_enabled` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_sort_order`(`sort_order` ASC) USING BTREE,
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '天大海棠杯项目报名表单配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of haitang_project_form_config
-- ----------------------------

-- ----------------------------
-- Table structure for haitang_project_registration
-- ----------------------------
DROP TABLE IF EXISTS `haitang_project_registration`;
CREATE TABLE `haitang_project_registration`  (
  `registration_id` bigint NOT NULL AUTO_INCREMENT COMMENT '报名ID',
  `config_id` bigint NOT NULL COMMENT '表单配置ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `user_nick_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '用户昵称（冗余字段）',
  `user_avatar_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '用户头像（冗余字段）',
  `form_data` json NULL COMMENT '报名表单数据（JSON格式）',
  `audit_status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '0' COMMENT '审核状态（0待审核 1审核通过 2审核拒绝）',
  `audit_remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '审核备注',
  `audit_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '审核人',
  `audit_time` datetime NULL DEFAULT NULL COMMENT '审核时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`registration_id`) USING BTREE,
  INDEX `idx_config_id`(`config_id` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_audit_status`(`audit_status` ASC) USING BTREE,
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '天大海棠杯项目报名记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of haitang_project_registration
-- ----------------------------

SET FOREIGN_KEY_CHECKS = 1;
