{"remainingRequest": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\haitang\\project-registration\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\haitang\\project-registration\\index.vue", "mtime": 1754272686655}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1753781940188}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753843467163}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753843480362}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753843467163}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753843484659}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_projectRegistration", "require", "name", "data", "loading", "ids", "multiple", "showSearch", "total", "projectRegistrationList", "viewOpen", "auditOpen", "queryParams", "pageNum", "pageSize", "userNickName", "auditStatus", "auditBy", "viewForm", "auditForm", "auditRules", "required", "message", "trigger", "created", "getList", "methods", "_this", "listProjectRegistration", "then", "response", "rows", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "handleSelectionChange", "selection", "map", "item", "registrationId", "length", "handleView", "row", "_this2", "getProjectRegistration", "handleAudit", "auditRemark", "submitAudit", "_this3", "$refs", "validate", "valid", "auditProjectRegistration", "$modal", "msgSuccess", "handleDelete", "_this4", "registrationIds", "confirm", "delProjectRegistration", "catch", "handleExport", "download", "_objectSpread2", "default", "concat", "Date", "getTime", "formatFormData", "formData", "JSON", "stringify", "parse", "e"], "sources": ["src/views/miniapp/haitang/project-registration/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"用户昵称\" prop=\"userNickName\">\n        <el-input\n          v-model=\"queryParams.userNickName\"\n          placeholder=\"请输入用户昵称\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"审核状态\" prop=\"auditStatus\">\n        <el-select v-model=\"queryParams.auditStatus\" placeholder=\"请选择审核状态\" clearable>\n          <el-option label=\"待审核\" value=\"0\" />\n          <el-option label=\"审核通过\" value=\"1\" />\n          <el-option label=\"审核拒绝\" value=\"2\" />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"审核人\" prop=\"auditBy\">\n        <el-input\n          v-model=\"queryParams.auditBy\"\n          placeholder=\"请输入审核人\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n          v-hasPermi=\"['miniapp:haitang:project-registration:remove']\"\n        >删除</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"handleExport\"\n          v-hasPermi=\"['miniapp:haitang:project-registration:export']\"\n        >导出</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"projectRegistrationList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"报名ID\" align=\"center\" prop=\"registrationId\" />\n      <el-table-column label=\"用户昵称\" align=\"center\" prop=\"userNickName\" />\n      <el-table-column label=\"审核状态\" align=\"center\" prop=\"auditStatus\">\n        <template slot-scope=\"scope\">\n          <el-tag v-if=\"scope.row.auditStatus === '0'\" type=\"warning\">待审核</el-tag>\n          <el-tag v-else-if=\"scope.row.auditStatus === '1'\" type=\"success\">审核通过</el-tag>\n          <el-tag v-else-if=\"scope.row.auditStatus === '2'\" type=\"danger\">审核拒绝</el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"审核人\" align=\"center\" prop=\"auditBy\" />\n      <el-table-column label=\"审核时间\" align=\"center\" prop=\"auditTime\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.auditTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-view\"\n            @click=\"handleView(scope.row)\"\n            v-hasPermi=\"['miniapp:haitang:project-registration:query']\"\n          >查看</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-check\"\n            @click=\"handleAudit(scope.row)\"\n            v-hasPermi=\"['miniapp:haitang:project-registration:audit']\"\n            v-if=\"scope.row.auditStatus === '0'\"\n          >审核</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n            v-hasPermi=\"['miniapp:haitang:project-registration:remove']\"\n          >删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    \n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 查看报名详情对话框 -->\n    <el-dialog title=\"报名详情\" :visible.sync=\"viewOpen\" width=\"800px\" append-to-body>\n      <el-form ref=\"viewForm\" :model=\"viewForm\" label-width=\"100px\">\n        <el-form-item label=\"用户昵称\">\n          <span>{{ viewForm.userNickName }}</span>\n        </el-form-item>\n        <el-form-item label=\"报名数据\">\n          <pre style=\"white-space: pre-wrap; word-wrap: break-word;\">{{ formatFormData(viewForm.formData) }}</pre>\n        </el-form-item>\n        <el-form-item label=\"审核状态\">\n          <el-tag v-if=\"viewForm.auditStatus === '0'\" type=\"warning\">待审核</el-tag>\n          <el-tag v-else-if=\"viewForm.auditStatus === '1'\" type=\"success\">审核通过</el-tag>\n          <el-tag v-else-if=\"viewForm.auditStatus === '2'\" type=\"danger\">审核拒绝</el-tag>\n        </el-form-item>\n        <el-form-item label=\"审核备注\" v-if=\"viewForm.auditRemark\">\n          <span>{{ viewForm.auditRemark }}</span>\n        </el-form-item>\n        <el-form-item label=\"审核人\" v-if=\"viewForm.auditBy\">\n          <span>{{ viewForm.auditBy }}</span>\n        </el-form-item>\n        <el-form-item label=\"审核时间\" v-if=\"viewForm.auditTime\">\n          <span>{{ parseTime(viewForm.auditTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\n        </el-form-item>\n        <el-form-item label=\"创建时间\">\n          <span>{{ parseTime(viewForm.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"viewOpen = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 审核对话框 -->\n    <el-dialog title=\"审核报名\" :visible.sync=\"auditOpen\" width=\"500px\" append-to-body>\n      <el-form ref=\"auditForm\" :model=\"auditForm\" :rules=\"auditRules\" label-width=\"80px\">\n        <el-form-item label=\"审核结果\" prop=\"auditStatus\">\n          <el-radio-group v-model=\"auditForm.auditStatus\">\n            <el-radio label=\"1\">审核通过</el-radio>\n            <el-radio label=\"2\">审核拒绝</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        <el-form-item label=\"审核备注\" prop=\"auditRemark\">\n          <el-input v-model=\"auditForm.auditRemark\" type=\"textarea\" placeholder=\"请输入审核备注\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitAudit\">确 定</el-button>\n        <el-button @click=\"auditOpen = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listProjectRegistration, getProjectRegistration, delProjectRegistration, auditProjectRegistration } from \"@/api/miniapp/haitang/projectRegistration\";\n\nexport default {\n  name: \"ProjectRegistration\",\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 天大海棠杯项目报名记录表格数据\n      projectRegistrationList: [],\n      // 是否显示查看详情弹出层\n      viewOpen: false,\n      // 是否显示审核弹出层\n      auditOpen: false,\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        userNickName: null,\n        auditStatus: null,\n        auditBy: null,\n      },\n      // 查看表单参数\n      viewForm: {},\n      // 审核表单参数\n      auditForm: {},\n      // 审核表单校验\n      auditRules: {\n        auditStatus: [\n          { required: true, message: \"审核结果不能为空\", trigger: \"change\" }\n        ],\n      }\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    /** 查询天大海棠杯项目报名记录列表 */\n    getList() {\n      this.loading = true;\n      listProjectRegistration(this.queryParams).then(response => {\n        this.projectRegistrationList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.registrationId)\n      this.multiple = !selection.length\n    },\n    /** 查看按钮操作 */\n    handleView(row) {\n      getProjectRegistration(row.registrationId).then(response => {\n        this.viewForm = response.data;\n        this.viewOpen = true;\n      });\n    },\n    /** 审核按钮操作 */\n    handleAudit(row) {\n      this.auditForm = {\n        registrationId: row.registrationId,\n        auditStatus: null,\n        auditRemark: null\n      };\n      this.auditOpen = true;\n    },\n    /** 提交审核 */\n    submitAudit() {\n      this.$refs[\"auditForm\"].validate(valid => {\n        if (valid) {\n          auditProjectRegistration(this.auditForm).then(response => {\n            this.$modal.msgSuccess(\"审核成功\");\n            this.auditOpen = false;\n            this.getList();\n          });\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const registrationIds = row.registrationId || this.ids;\n      this.$modal.confirm('是否确认删除天大海棠杯项目报名记录编号为\"' + registrationIds + '\"的数据项？').then(function() {\n        return delProjectRegistration(registrationIds);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download('miniapp/haitang/project-registration/export', {\n        ...this.queryParams\n      }, `project-registration_${new Date().getTime()}.xlsx`)\n    },\n    /** 格式化表单数据 */\n    formatFormData(formData) {\n      if (!formData) return '';\n      try {\n        return JSON.stringify(JSON.parse(formData), null, 2);\n      } catch (e) {\n        return formData;\n      }\n    }\n  }\n};\n</script>\n"], "mappings": ";;;;;;;;;;;;;;AAyKA,IAAAA,oBAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,uBAAA;MACA;MACAC,QAAA;MACA;MACAC,SAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,YAAA;QACAC,WAAA;QACAC,OAAA;MACA;MACA;MACAC,QAAA;MACA;MACAC,SAAA;MACA;MACAC,UAAA;QACAJ,WAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,sBACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAAvB,OAAA;MACA,IAAAwB,4CAAA,OAAAhB,WAAA,EAAAiB,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAlB,uBAAA,GAAAqB,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAAnB,KAAA,GAAAsB,QAAA,CAAAtB,KAAA;QACAmB,KAAA,CAAAvB,OAAA;MACA;IACA;IACA,aACA4B,WAAA,WAAAA,YAAA;MACA,KAAApB,WAAA,CAAAC,OAAA;MACA,KAAAY,OAAA;IACA;IACA,aACAQ,UAAA,WAAAA,WAAA;MACA,KAAAC,SAAA;MACA,KAAAF,WAAA;IACA;IACA;IACAG,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA/B,GAAA,GAAA+B,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,cAAA;MAAA;MACA,KAAAjC,QAAA,IAAA8B,SAAA,CAAAI,MAAA;IACA;IACA,aACAC,UAAA,WAAAA,WAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,2CAAA,EAAAF,GAAA,CAAAH,cAAA,EAAAV,IAAA,WAAAC,QAAA;QACAa,MAAA,CAAAzB,QAAA,GAAAY,QAAA,CAAA3B,IAAA;QACAwC,MAAA,CAAAjC,QAAA;MACA;IACA;IACA,aACAmC,WAAA,WAAAA,YAAAH,GAAA;MACA,KAAAvB,SAAA;QACAoB,cAAA,EAAAG,GAAA,CAAAH,cAAA;QACAvB,WAAA;QACA8B,WAAA;MACA;MACA,KAAAnC,SAAA;IACA;IACA,WACAoC,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,cAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAC,6CAAA,EAAAJ,MAAA,CAAA7B,SAAA,EAAAU,IAAA,WAAAC,QAAA;YACAkB,MAAA,CAAAK,MAAA,CAAAC,UAAA;YACAN,MAAA,CAAArC,SAAA;YACAqC,MAAA,CAAAvB,OAAA;UACA;QACA;MACA;IACA;IACA,aACA8B,YAAA,WAAAA,aAAAb,GAAA;MAAA,IAAAc,MAAA;MACA,IAAAC,eAAA,GAAAf,GAAA,CAAAH,cAAA,SAAAlC,GAAA;MACA,KAAAgD,MAAA,CAAAK,OAAA,2BAAAD,eAAA,aAAA5B,IAAA;QACA,WAAA8B,2CAAA,EAAAF,eAAA;MACA,GAAA5B,IAAA;QACA2B,MAAA,CAAA/B,OAAA;QACA+B,MAAA,CAAAH,MAAA,CAAAC,UAAA;MACA,GAAAM,KAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,oDAAAC,cAAA,CAAAC,OAAA,MACA,KAAApD,WAAA,2BAAAqD,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;IACA,cACAC,cAAA,WAAAA,eAAAC,QAAA;MACA,KAAAA,QAAA;MACA;QACA,OAAAC,IAAA,CAAAC,SAAA,CAAAD,IAAA,CAAAE,KAAA,CAAAH,QAAA;MACA,SAAAI,CAAA;QACA,OAAAJ,QAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}