{"remainingRequest": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\haitang\\project-registration\\index.vue?vue&type=template&id=060535c8", "dependencies": [{"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\haitang\\project-registration\\index.vue", "mtime": 1754272686655}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753843467163}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753843491419}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753843467163}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753843484659}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}