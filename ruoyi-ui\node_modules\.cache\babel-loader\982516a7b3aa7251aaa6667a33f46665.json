{"remainingRequest": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\user\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\user\\index.vue", "mtime": 1754036860043}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753843467163}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753843480362}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753843467163}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753843484659}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1753781940188}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753843467163}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753843480362}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753843467163}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753843484659}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_user", "require", "_industry", "name", "dicts", "components", "ImageUpload", "Promise", "resolve", "then", "_interopRequireWildcard2", "default", "ImagePreview", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "userList", "date<PERSON><PERSON><PERSON>", "openView", "viewForm", "queryParams", "pageNum", "pageSize", "searchValue", "undefined", "userName", "weixinNickname", "realName", "phonenumber", "status", "graduationYear", "region", "industryField", "graduationYears", "provinces", "firstLevelIndustries", "columns", "key", "label", "visible", "rules", "required", "message", "trigger", "min", "max", "pattern", "created", "getList", "initGraduationYears", "initFirstLevelIndustries", "methods", "_this", "listMiniUserAdmin", "addDateRange", "response", "rows", "handleStatusChange", "row", "_this2", "text", "displayName", "$modal", "confirm", "changeMiniUserStatus", "userId", "msgSuccess", "catch", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "handleSelectionChange", "selection", "map", "item", "length", "handleView", "_this3", "_asyncToGenerator2", "_regenerator2", "m", "_callee", "_t", "w", "_context", "p", "n", "getMiniUser", "v", "parseIndustryTags", "console", "error", "msgError", "a", "user", "_callee2", "industryIds", "_t2", "_context2", "industryTags", "split", "filter", "id", "trim", "parseInt", "isNaN", "getBatchIndustryInfo", "Array", "isArray", "nodeName", "nodeType", "nodeLevel", "streamType", "rootNode", "handleDisable", "_this4", "confirmMessage", "userIds", "batchDisableMiniUser", "handleExport", "download", "_objectSpread2", "concat", "Date", "getTime", "currentYear", "getFullYear", "startYear", "year", "push", "toString", "_this5", "_callee3", "_t3", "_context3", "getNodesByLevel", "previewWeixinAvatar", "avatarUrl", "loadingHtml", "$msgbox", "title", "dangerouslyUseHTMLString", "showCancelButton", "showConfirmButton", "confirmButtonText", "customClass", "img", "Image", "onload", "imgHtml", "messageBox", "document", "querySelector", "innerHTML", "onerror", "errorHtml", "src", "referrerPolicy"], "sources": ["src/views/miniapp/user/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!--用户数据-->\r\n        <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n          <el-form-item label=\"关键字\" prop=\"searchValue\">\r\n            <el-input\r\n              v-model=\"queryParams.searchValue\"\r\n              placeholder=\"搜索姓名、昵称、手机号等\"\r\n              clearable\r\n              style=\"width: 280px\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            >\r\n              <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n            </el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"用户名称\" prop=\"userName\">\r\n            <el-input\r\n              v-model=\"queryParams.userName\"\r\n              placeholder=\"用户名称\"\r\n              clearable\r\n              style=\"width: 160px\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"微信昵称\" prop=\"weixinNickname\">\r\n            <el-input\r\n              v-model=\"queryParams.weixinNickname\"\r\n              placeholder=\"微信昵称\"\r\n              clearable\r\n              style=\"width: 160px\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"真实姓名\" prop=\"realName\">\r\n            <el-input\r\n              v-model=\"queryParams.realName\"\r\n              placeholder=\"真实姓名\"\r\n              clearable\r\n              style=\"width: 160px\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"手机号码\" prop=\"phonenumber\">\r\n            <el-input\r\n              v-model=\"queryParams.phonenumber\"\r\n              placeholder=\"手机号码\"\r\n              clearable\r\n              style=\"width: 160px\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"状态\" prop=\"status\">\r\n            <el-select\r\n              v-model=\"queryParams.status\"\r\n              placeholder=\"用户状态\"\r\n              clearable\r\n              style=\"width: 150px\"\r\n            >\r\n              <el-option\r\n                v-for=\"dict in dict.type.sys_normal_disable\"\r\n                :key=\"dict.value\"\r\n                :label=\"dict.label\"\r\n                :value=\"dict.value\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"毕业年份\" prop=\"graduationYear\">\r\n            <el-select\r\n              v-model=\"queryParams.graduationYear\"\r\n              placeholder=\"请选择毕业年份\"\r\n              clearable\r\n              style=\"width: 150px\"\r\n            >\r\n              <el-option\r\n                v-for=\"year in graduationYears\"\r\n                :key=\"year\"\r\n                :label=\"year + '年'\"\r\n                :value=\"year\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"地区\" prop=\"region\">\r\n            <el-select\r\n              v-model=\"queryParams.region\"\r\n              placeholder=\"请选择地区\"\r\n              clearable\r\n              filterable\r\n              style=\"width: 150px\"\r\n            >\r\n              <el-option\r\n                v-for=\"province in provinces\"\r\n                :key=\"province\"\r\n                :label=\"province\"\r\n                :value=\"province\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"行业领域\" prop=\"industryField\">\r\n            <el-select\r\n              v-model=\"queryParams.industryField\"\r\n              placeholder=\"请选择行业领域\"\r\n              clearable\r\n              filterable\r\n              style=\"width: 150px\"\r\n            >\r\n              <el-option\r\n                v-for=\"industry in firstLevelIndustries\"\r\n                :key=\"industry.id\"\r\n                :label=\"industry.nodeName\"\r\n                :value=\"industry.id\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"创建时间\">\r\n            <el-date-picker\r\n              v-model=\"dateRange\"\r\n              style=\"width: 200px\"\r\n              value-format=\"yyyy-MM-dd\"\r\n              type=\"daterange\"\r\n              range-separator=\"-\"\r\n              start-placeholder=\"开始日期\"\r\n              end-placeholder=\"结束日期\"\r\n            ></el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <!-- 修改按钮已隐藏：用户信息不应由管理员修改 -->\r\n            <!-- <el-button\r\n              type=\"success\"\r\n              plain\r\n              icon=\"el-icon-edit\"\r\n              size=\"mini\"\r\n              :disabled=\"single\"\r\n              @click=\"handleUpdate\"\r\n              v-hasPermi=\"['miniapp:user:edit']\"\r\n            >修改</el-button> -->\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"warning\"\r\n              plain\r\n              icon=\"el-icon-close\"\r\n              size=\"mini\"\r\n              :disabled=\"multiple\"\r\n              @click=\"handleDisable\"\r\n              v-hasPermi=\"['miniapp:user:edit']\"\r\n            >停用</el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"warning\"\r\n              plain\r\n              icon=\"el-icon-download\"\r\n              size=\"mini\"\r\n              @click=\"handleExport\"\r\n              v-hasPermi=\"['miniapp:user:export']\"\r\n            >导出</el-button>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\" :columns=\"columns\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table v-loading=\"loading\" :data=\"userList\" @selection-change=\"handleSelectionChange\">\r\n          <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\r\n          <el-table-column label=\"用户编号\" align=\"center\" key=\"userId\" prop=\"userId\" v-if=\"columns[0].visible\" width=\"80\" />\r\n          <el-table-column label=\"微信昵称\" align=\"center\" key=\"weixinNickname\" prop=\"weixinNickname\" v-if=\"columns[1].visible\" :show-overflow-tooltip=\"true\" />\r\n          <el-table-column label=\"微信头像\" align=\"center\" key=\"weixinAvatar\" v-if=\"columns[2].visible\" width=\"80\">\r\n            <template slot-scope=\"scope\">\r\n              <img v-if=\"scope.row.weixinAvatar\" :src=\"scope.row.weixinAvatar\" alt=\"\" referrerpolicy=\"no-referrer\" style=\"width: 40px; height: 40px; border-radius: 50%; object-fit: cover;\" />\r\n              <el-avatar v-else :size=\"40\" icon=\"el-icon-user-solid\"></el-avatar>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"姓名\" align=\"center\" key=\"realName\" prop=\"realName\" v-if=\"columns[3].visible\" :show-overflow-tooltip=\"true\" />\r\n          <el-table-column label=\"手机号码\" align=\"center\" key=\"phonenumber\" prop=\"phonenumber\" v-if=\"columns[4].visible\" width=\"120\" />\r\n          <el-table-column label=\"形象照\" align=\"center\" key=\"portraitUrl\" v-if=\"columns[5].visible\" width=\"80\">\r\n            <template slot-scope=\"scope\">\r\n              <image-preview :src=\"scope.row.portraitUrl\" :width=\"50\" :height=\"50\" v-if=\"scope.row.portraitUrl\"/>\r\n              <el-avatar v-else :size=\"50\" icon=\"el-icon-picture\"></el-avatar>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"毕业院校\" align=\"center\" key=\"graduateSchool\" prop=\"graduateSchool\" v-if=\"columns[6].visible\" :show-overflow-tooltip=\"true\" />\r\n          <el-table-column label=\"所属企业\" align=\"center\" key=\"currentCompany\" prop=\"currentCompany\" v-if=\"columns[7].visible\" :show-overflow-tooltip=\"true\" />\r\n          <el-table-column label=\"行业领域\" align=\"center\" key=\"industryField\" v-if=\"columns[8].visible\" :show-overflow-tooltip=\"true\">\r\n            <template slot-scope=\"scope\">\r\n              <span v-if=\"scope.row.industryNames\">{{ scope.row.industryNames }}</span>\r\n              <span v-else style=\"color: #C0C4CC;\">未设置</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"状态\" align=\"center\" key=\"status\" v-if=\"columns[9].visible\">\r\n            <template slot-scope=\"scope\">\r\n              <el-switch\r\n                v-model=\"scope.row.status\"\r\n                active-value=\"0\"\r\n                inactive-value=\"1\"\r\n                @change=\"handleStatusChange(scope.row)\"\r\n              ></el-switch>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" v-if=\"columns[10].visible\" width=\"160\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.createTime) }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column\r\n            label=\"操作\"\r\n            align=\"center\"\r\n            width=\"180\"\r\n            class-name=\"small-padding fixed-width\"\r\n          >\r\n            <template slot-scope=\"scope\" v-if=\"scope.row.userId !== 1\">\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-view\"\r\n                @click=\"handleView(scope.row)\"\r\n                v-hasPermi=\"['miniapp:user:query']\"\r\n              >详情</el-button>\r\n              <!-- 修改按钮已隐藏：用户信息不应由管理员修改 -->\r\n              <!-- <el-button\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-edit\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n                v-hasPermi=\"['miniapp:user:edit']\"\r\n              >修改</el-button> -->\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-close\"\r\n                @click=\"handleDisable(scope.row)\"\r\n                v-hasPermi=\"['miniapp:user:edit']\"\r\n                :disabled=\"scope.row.status === '1'\"\r\n              >停用</el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :total=\"total\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          @pagination=\"getList\"\r\n        />\r\n\r\n    <!-- 修改用户对话框已移除：用户信息不应由管理员修改 -->\r\n\r\n\r\n    <!-- 用户详情对话框 -->\r\n    <el-dialog title=\"用户详情\" :visible.sync=\"openView\" width=\"1000px\" append-to-body>\r\n      <div class=\"user-detail-container\">\r\n        <!-- 基本信息 -->\r\n        <el-card class=\"detail-card\" shadow=\"never\">\r\n          <div slot=\"header\" class=\"card-header\">\r\n            <span class=\"card-title\">基本信息</span>\r\n          </div>\r\n          <el-descriptions :column=\"3\" border>\r\n            <el-descriptions-item label=\"用户编号\">{{ viewForm.userId }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"姓名\">{{ viewForm.realName || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"微信昵称\">{{ viewForm.weixinNickname || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"手机号码\">{{ viewForm.phonenumber || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"性别\">\r\n              <dict-tag :options=\"dict.type.sys_user_sex\" :value=\"viewForm.sex\"/>\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"出生日期\">{{ parseTime(viewForm.birthDate, '{y}-{m}-{d}') || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"籍贯\">{{ viewForm.region || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"状态\">\r\n              <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"viewForm.status\"/>\r\n            </el-descriptions-item>\r\n          </el-descriptions>\r\n        </el-card>\r\n\r\n        <!-- 头像信息 -->\r\n        <el-card class=\"detail-card\" shadow=\"never\">\r\n          <div slot=\"header\" class=\"card-header\">\r\n            <span class=\"card-title\">头像信息</span>\r\n          </div>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <div class=\"avatar-item\">\r\n                <div class=\"avatar-label\">微信头像</div>\r\n                <div class=\"avatar-content\">\r\n                  <img v-if=\"viewForm.weixinAvatar\" :src=\"viewForm.weixinAvatar\" alt=\"\" referrerpolicy=\"no-referrer\" style=\"width: 80px; height: 80px; border-radius: 8px; object-fit: cover; cursor: pointer;\" @click=\"previewWeixinAvatar(viewForm.weixinAvatar)\" />\r\n                  <div v-else class=\"no-avatar\">未设置</div>\r\n                </div>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <div class=\"avatar-item\">\r\n                <div class=\"avatar-label\">形象照</div>\r\n                <div class=\"avatar-content\">\r\n                  <image-preview :src=\"viewForm.portraitUrl\" :width=\"80\" :height=\"80\" v-if=\"viewForm.portraitUrl\"/>\r\n                  <div v-else class=\"no-avatar\">未设置</div>\r\n                </div>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n        </el-card>\r\n\r\n        <!-- 教育背景 -->\r\n        <el-card class=\"detail-card\" shadow=\"never\">\r\n          <div slot=\"header\" class=\"card-header\">\r\n            <span class=\"card-title\">教育背景</span>\r\n          </div>\r\n          <el-descriptions :column=\"2\" border>\r\n            <el-descriptions-item label=\"毕业院校\">{{ viewForm.graduateSchool || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"毕业年份\">{{ viewForm.graduationYear || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"专业\">{{ viewForm.major || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"学院\">{{ viewForm.college || '未设置' }}</el-descriptions-item>\r\n          </el-descriptions>\r\n        </el-card>\r\n\r\n        <!-- 职业信息 -->\r\n        <el-card class=\"detail-card\" shadow=\"never\">\r\n          <div slot=\"header\" class=\"card-header\">\r\n            <span class=\"card-title\">职业信息</span>\r\n          </div>\r\n          <el-descriptions :column=\"2\" border>\r\n            <el-descriptions-item label=\"当前公司\">{{ viewForm.currentCompany || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"行业领域\">\r\n              <div v-if=\"viewForm.industryTags && viewForm.industryTags.length > 0\" class=\"industry-tags\">\r\n                <el-tag\r\n                  v-for=\"tag in viewForm.industryTags\"\r\n                  :key=\"tag.id\"\r\n                  size=\"small\"\r\n                  style=\"margin-right: 5px; margin-bottom: 5px;\"\r\n                >\r\n                  {{ tag.nodeName }}\r\n                </el-tag>\r\n              </div>\r\n              <span v-else>未设置</span>\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"职位名称\">{{ viewForm.positionTitle || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"个人介绍\" :span=\"2\">\r\n              <div class=\"personal-intro\">\r\n                {{ viewForm.personalIntroduction || '未设置' }}\r\n              </div>\r\n            </el-descriptions-item>\r\n          </el-descriptions>\r\n        </el-card>\r\n\r\n        <!-- 积分信息 -->\r\n        <el-card class=\"detail-card\" shadow=\"never\">\r\n          <div slot=\"header\" class=\"card-header\">\r\n            <span class=\"card-title\">积分信息</span>\r\n          </div>\r\n          <el-descriptions :column=\"1\" border>\r\n            <el-descriptions-item label=\"总积分\">{{ viewForm.totalPoints || 0 }}</el-descriptions-item>\r\n          </el-descriptions>\r\n        </el-card>\r\n\r\n        <!-- 系统信息 -->\r\n        <el-card class=\"detail-card\" shadow=\"never\">\r\n          <div slot=\"header\" class=\"card-header\">\r\n            <span class=\"card-title\">系统信息</span>\r\n          </div>\r\n          <el-descriptions :column=\"2\" border>\r\n            <el-descriptions-item label=\"最后登录时间\">{{ parseTime(viewForm.lastLoginTime) || '未登录' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"创建时间\">{{ parseTime(viewForm.createTime) || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"更新时间\">{{ parseTime(viewForm.updateTime) || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"备注\" :span=\"2\">{{ viewForm.remark || '无备注' }}</el-descriptions-item>\r\n          </el-descriptions>\r\n        </el-card>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"openView = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listMiniUserAdmin, getMiniUser, changeMiniUserStatus, batchDisableMiniUser } from \"@/api/miniapp/user\";\r\nimport { getNodesByLevel, getBatchIndustryInfo } from \"@/api/miniapp/industry\";\r\n\r\nexport default {\r\n  name: \"MiniUser\",\r\n  dicts: ['sys_normal_disable', 'sys_user_sex'],\r\n  components: {\r\n    ImageUpload: () => import(\"@/components/ImageUpload\"),\r\n    ImagePreview: () => import(\"@/components/ImagePreview\")\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 用户表格数据\r\n      userList: [],\r\n      // 日期范围\r\n      dateRange: [],\r\n      // 用户详情对话框\r\n      openView: false,\r\n      // 用户详情数据\r\n      viewForm: {},\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        searchValue: undefined,\r\n        userName: undefined,\r\n        weixinNickname: undefined,\r\n        realName: undefined,\r\n        phonenumber: undefined,\r\n        status: undefined,\r\n        graduationYear: undefined,\r\n        region: undefined,\r\n        industryField: undefined\r\n      },\r\n      // 毕业年份选项\r\n      graduationYears: [],\r\n      // 省份选项\r\n      provinces: [\r\n        '北京', '天津', '河北', '山西', '内蒙古', '辽宁', '吉林', '黑龙江',\r\n        '上海', '江苏', '浙江', '安徽', '福建', '江西', '山东', '河南',\r\n        '湖北', '湖南', '广东', '广西', '海南', '重庆', '四川', '贵州',\r\n        '云南', '西藏', '陕西', '甘肃', '青海', '宁夏', '新疆', '台湾',\r\n        '香港', '澳门'\r\n      ],\r\n      // 一级行业选项\r\n      firstLevelIndustries: [],\r\n      // 列信息\r\n      columns: [\r\n        { key: 0, label: `用户编号`, visible: true },\r\n        { key: 1, label: `微信昵称`, visible: true },\r\n        { key: 2, label: `微信头像`, visible: true },\r\n        { key: 3, label: `姓名`, visible: true },\r\n        { key: 4, label: `手机号码`, visible: true },\r\n        { key: 5, label: `形象照`, visible: true },\r\n        { key: 6, label: `毕业院校`, visible: true },\r\n        { key: 7, label: `所属企业`, visible: true },\r\n        { key: 8, label: `行业领域`, visible: true },\r\n        { key: 9, label: `状态`, visible: true },\r\n        { key: 10, label: `创建时间`, visible: true }\r\n      ],\r\n      // 表单校验\r\n      rules: {\r\n        weixinNickname: [\r\n          { required: true, message: \"微信昵称不能为空\", trigger: \"blur\" },\r\n          { min: 1, max: 30, message: \"微信昵称长度必须在1到30个字符之间\", trigger: \"blur\" }\r\n        ],\r\n        realName: [\r\n          { required: true, message: \"姓名不能为空\", trigger: \"blur\" },\r\n          { min: 2, max: 30, message: \"姓名长度必须在2到30个字符之间\", trigger: \"blur\" }\r\n        ],\r\n        phonenumber: [\r\n          {\r\n            pattern: /^1[3-9]\\d{9}$/,\r\n            message: \"请输入正确的手机号码\",\r\n            trigger: \"blur\"\r\n          }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n    this.initGraduationYears();\r\n    this.initFirstLevelIndustries();\r\n  },\r\n  methods: {\r\n    /** 查询用户列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      // 后台管理使用新的 API，查询所有用户（包括停用的）\r\n      listMiniUserAdmin(this.addDateRange(this.queryParams, this.dateRange)).then(response => {\r\n        this.userList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 用户状态修改\r\n    handleStatusChange(row) {\r\n      let text = row.status === \"0\" ? \"启用\" : \"停用\";\r\n      // 使用真实姓名或微信昵称作为显示名称\r\n      let displayName = row.realName || row.weixinNickname || row.userName || '该用户';\r\n      this.$modal.confirm('确认要\"' + text + '\"\"' + displayName + '\"用户吗？').then(function() {\r\n        return changeMiniUserStatus(row.userId, row.status);\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + \"成功\");\r\n      }).catch(function() {\r\n        row.status = row.status === \"0\" ? \"1\" : \"0\";\r\n      });\r\n    },\r\n    // 取消按钮和表单重置方法已移除：不再需要修改功能\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = [];\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.userId);\r\n      this.single = selection.length != 1;\r\n      this.multiple = !selection.length;\r\n    },\r\n\r\n\r\n    /** 查看详情按钮操作 */\r\n    async handleView(row) {\r\n      const userId = row.userId;\r\n      try {\r\n        const response = await getMiniUser(userId);\r\n        this.viewForm = response.data;\r\n        // 解析行业标签\r\n        await this.parseIndustryTags(this.viewForm);\r\n        this.openView = true;\r\n      } catch (error) {\r\n        console.error('获取用户详情失败', error);\r\n        this.$modal.msgError('获取用户详情失败');\r\n      }\r\n    },\r\n    /** 解析行业标签 */\r\n    async parseIndustryTags(user) {\r\n      if (!user.industryField) {\r\n        user.industryTags = [];\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const industryIds = user.industryField.split(',')\r\n          .filter(id => id.trim())\r\n          .map(id => parseInt(id.trim()))\r\n          .filter(id => !isNaN(id));\r\n\r\n        if (industryIds.length === 0) {\r\n          user.industryTags = [];\r\n          return;\r\n        }\r\n\r\n        // 批量查询行业信息\r\n        const response = await getBatchIndustryInfo(industryIds);\r\n        if (response.data && Array.isArray(response.data)) {\r\n          user.industryTags = response.data.map(item => ({\r\n            id: item.id,\r\n            nodeName: item.nodeName,\r\n            nodeType: item.nodeType,\r\n            nodeLevel: item.nodeLevel,\r\n            streamType: item.streamType,\r\n            rootNode: item.rootNode\r\n          }));\r\n        } else {\r\n          user.industryTags = [];\r\n        }\r\n      } catch (error) {\r\n        console.error('解析行业标签失败', error);\r\n        user.industryTags = [];\r\n      }\r\n    },\r\n    /** 解析编辑表单中的行业标签 - 已禁用 */\r\n    /*\r\n    async parseFormIndustryTags() {\r\n      if (!this.form.industryField) {\r\n        this.form.industryTags = [];\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const industryIds = this.form.industryField.split(',').filter(id => id.trim());\r\n        const industryTags = [];\r\n\r\n        for (const industryId of industryIds) {\r\n          if (industryId.trim()) {\r\n            try {\r\n              const response = await getIndustryNodeInfo(industryId.trim());\r\n              if (response.data) {\r\n                industryTags.push({\r\n                  id: response.data.id,\r\n                  nodeName: response.data.nodeName,\r\n                  nodeType: response.data.nodeType,\r\n                  nodeLevel: response.data.nodeLevel\r\n                });\r\n              }\r\n            } catch (error) {\r\n              console.warn(`获取行业信息失败，ID: ${industryId}`, error);\r\n            }\r\n          }\r\n        }\r\n\r\n        this.form.industryTags = industryTags;\r\n      } catch (error) {\r\n        console.error('解析编辑表单行业标签失败', error);\r\n        this.form.industryTags = [];\r\n      }\r\n    },\r\n    */\r\n    /** 修改按钮操作 - 已禁用 */\r\n    /*\r\n    async handleUpdate(row) {\r\n      this.reset();\r\n      const userId = row.userId || this.ids;\r\n      try {\r\n        const response = await getMiniUser(userId);\r\n        this.form = response.data;\r\n        // 解析行业标签\r\n        await this.parseFormIndustryTags();\r\n        this.open = true;\r\n        this.title = \"修改用户\";\r\n        this.form.password = \"\";\r\n      } catch (error) {\r\n        console.error('获取用户信息失败', error);\r\n        this.$modal.msgError('获取用户信息失败');\r\n      }\r\n    },\r\n    */\r\n\r\n    /** 提交按钮 - 已禁用 */\r\n    /*\r\n    submitForm: function() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.userId != undefined) {\r\n            updateMiniUser(this.form).then(() => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    */\r\n    /** 停用按钮操作 */\r\n    handleDisable(row) {\r\n      let confirmMessage;\r\n      let userIds = [];\r\n\r\n      if (row.userId) {\r\n        // 单个停用\r\n        userIds = [row.userId];\r\n        let displayName = row.realName || row.weixinNickname || row.userName || '该用户';\r\n        confirmMessage = '是否确认停用用户\"' + displayName + '\"？停用后该用户将无法登录小程序。';\r\n      } else {\r\n        // 批量停用\r\n        userIds = this.ids;\r\n        confirmMessage = '是否确认停用选中的' + this.ids.length + '个用户？停用后这些用户将无法登录小程序。';\r\n      }\r\n\r\n      this.$modal.confirm(confirmMessage).then(() => {\r\n        // 调用批量停用API\r\n        return batchDisableMiniUser(userIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"停用成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('miniapp/user/export', {\r\n        ...this.queryParams\r\n      }, `user_${new Date().getTime()}.xlsx`)\r\n    },\r\n    /** 初始化毕业年份选项 */\r\n    initGraduationYears() {\r\n      const currentYear = new Date().getFullYear();\r\n      const startYear = 1980;\r\n      this.graduationYears = [];\r\n      for (let year = currentYear; year >= startYear; year--) {\r\n        this.graduationYears.push(year.toString());\r\n      }\r\n    },\r\n    /** 初始化一级行业选项 */\r\n    async initFirstLevelIndustries() {\r\n      try {\r\n        const response = await getNodesByLevel(1);\r\n        this.firstLevelIndustries = response.data || [];\r\n      } catch (error) {\r\n        console.error('获取一级行业失败', error);\r\n        this.firstLevelIndustries = [];\r\n      }\r\n    },\r\n    /** 预览微信头像 */\r\n    previewWeixinAvatar(avatarUrl) {\r\n      if (avatarUrl) {\r\n        // 先显示加载中的对话框\r\n        const loadingHtml = `\r\n          <div style=\"text-align: center; padding: 20px;\">\r\n            <i class=\"el-icon-loading\" style=\"font-size: 24px; color: #409EFF;\"></i>\r\n            <div style=\"margin-top: 10px; color: #666;\">头像加载中...</div>\r\n          </div>\r\n        `;\r\n\r\n        this.$msgbox({\r\n          title: '微信头像预览',\r\n          dangerouslyUseHTMLString: true,\r\n          message: loadingHtml,\r\n          showCancelButton: false,\r\n          showConfirmButton: true,\r\n          confirmButtonText: '关闭',\r\n          customClass: 'avatar-preview-dialog'\r\n        });\r\n\r\n        // 预加载图片\r\n        const img = new Image();\r\n        img.onload = () => {\r\n          // 图片加载成功后更新对话框内容\r\n          const imgHtml = `\r\n            <img\r\n              src=\"${avatarUrl}\"\r\n              alt=\"微信头像预览\"\r\n              referrerpolicy=\"no-referrer\"\r\n              style=\"max-width: 100%; max-height: 400px; object-fit: contain; display: block; margin: 0 auto; border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\"\r\n            />\r\n          `;\r\n\r\n          // 更新对话框内容\r\n          const messageBox = document.querySelector('.avatar-preview-dialog .el-message-box__message');\r\n          if (messageBox) {\r\n            messageBox.innerHTML = imgHtml;\r\n          }\r\n        };\r\n\r\n        img.onerror = () => {\r\n          // 图片加载失败\r\n          const errorHtml = `\r\n            <div style=\"text-align: center; padding: 20px; color: #F56C6C;\">\r\n              <i class=\"el-icon-picture-outline\" style=\"font-size: 48px; margin-bottom: 10px;\"></i>\r\n              <div>头像加载失败</div>\r\n              <div style=\"font-size: 12px; margin-top: 5px; color: #999;\">请检查网络连接或图片链接</div>\r\n            </div>\r\n          `;\r\n\r\n          const messageBox = document.querySelector('.avatar-preview-dialog .el-message-box__message');\r\n          if (messageBox) {\r\n            messageBox.innerHTML = errorHtml;\r\n          }\r\n        };\r\n\r\n        img.src = avatarUrl;\r\n        img.referrerPolicy = \"no-referrer\";\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.user-detail-container {\r\n  max-height: 70vh;\r\n  overflow-y: auto;\r\n}\r\n\r\n.detail-card {\r\n  margin-bottom: 20px;\r\n  border-radius: 8px;\r\n}\r\n\r\n.detail-card:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0;\r\n}\r\n\r\n.card-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n  position: relative;\r\n  padding-left: 12px;\r\n}\r\n\r\n.card-title::before {\r\n  content: '';\r\n  position: absolute;\r\n  left: 0;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  width: 4px;\r\n  height: 16px;\r\n  background: #409EFF;\r\n  border-radius: 2px;\r\n}\r\n\r\n.avatar-item {\r\n  text-align: center;\r\n  padding: 20px;\r\n}\r\n\r\n.avatar-label {\r\n  font-size: 14px;\r\n  color: #606266;\r\n  margin-bottom: 15px;\r\n  font-weight: 500;\r\n}\r\n\r\n.avatar-content {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  min-height: 80px;\r\n}\r\n\r\n.no-avatar {\r\n  color: #C0C4CC;\r\n  font-size: 14px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 80px;\r\n  height: 80px;\r\n  border: 2px dashed #E4E7ED;\r\n  border-radius: 50%;\r\n  background-color: #FAFAFA;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .user-detail-container {\r\n    max-height: 60vh;\r\n  }\r\n\r\n  .avatar-item {\r\n    padding: 15px;\r\n  }\r\n\r\n  .avatar-content {\r\n    min-height: 60px;\r\n  }\r\n\r\n  .no-avatar {\r\n    width: 60px;\r\n    height: 60px;\r\n  }\r\n\r\n  .industry-field-container .industry-tags-display {\r\n    margin-bottom: 8px;\r\n  }\r\n\r\n  .industry-tags .el-tag {\r\n    margin-right: 5px;\r\n    margin-bottom: 5px;\r\n  }\r\n\r\n  .personal-intro {\r\n    max-width: 100%;\r\n    word-wrap: break-word;\r\n    white-space: pre-wrap;\r\n    line-height: 1.5;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAyXA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAE,IAAA;EACAC,KAAA;EACAC,UAAA;IACAC,WAAA,WAAAA,YAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAV,OAAA;MAAA;IAAA;IACAW,YAAA,WAAAA,aAAA;MAAA,OAAAL,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAV,OAAA;MAAA;IAAA;EACA;EACAY,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,QAAA;MACA;MACAC,SAAA;MACA;MACAC,QAAA;MACA;MACAC,QAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,WAAA,EAAAC,SAAA;QACAC,QAAA,EAAAD,SAAA;QACAE,cAAA,EAAAF,SAAA;QACAG,QAAA,EAAAH,SAAA;QACAI,WAAA,EAAAJ,SAAA;QACAK,MAAA,EAAAL,SAAA;QACAM,cAAA,EAAAN,SAAA;QACAO,MAAA,EAAAP,SAAA;QACAQ,aAAA,EAAAR;MACA;MACA;MACAS,eAAA;MACA;MACAC,SAAA,GACA,kDACA,gDACA,gDACA,gDACA,WACA;MACA;MACAC,oBAAA;MACA;MACAC,OAAA,GACA;QAAAC,GAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,EACA;MACA;MACAC,KAAA;QACAd,cAAA,GACA;UAAAe,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACAhB,QAAA,GACA;UAAAc,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACAf,WAAA,GACA;UACAkB,OAAA;UACAJ,OAAA;UACAC,OAAA;QACA;MAEA;IACA;EACA;EACAI,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,mBAAA;IACA,KAAAC,wBAAA;EACA;EACAC,OAAA;IACA,aACAH,OAAA,WAAAA,QAAA;MAAA,IAAAI,KAAA;MACA,KAAA1C,OAAA;MACA;MACA,IAAA2C,uBAAA,OAAAC,YAAA,MAAAlC,WAAA,OAAAH,SAAA,GAAAZ,IAAA,WAAAkD,QAAA;QACAH,KAAA,CAAApC,QAAA,GAAAuC,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAArC,KAAA,GAAAwC,QAAA,CAAAxC,KAAA;QACAqC,KAAA,CAAA1C,OAAA;MACA;IACA;IACA;IACA+C,kBAAA,WAAAA,mBAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,IAAA,GAAAF,GAAA,CAAA7B,MAAA;MACA;MACA,IAAAgC,WAAA,GAAAH,GAAA,CAAA/B,QAAA,IAAA+B,GAAA,CAAAhC,cAAA,IAAAgC,GAAA,CAAAjC,QAAA;MACA,KAAAqC,MAAA,CAAAC,OAAA,UAAAH,IAAA,UAAAC,WAAA,YAAAxD,IAAA;QACA,WAAA2D,0BAAA,EAAAN,GAAA,CAAAO,MAAA,EAAAP,GAAA,CAAA7B,MAAA;MACA,GAAAxB,IAAA;QACAsD,MAAA,CAAAG,MAAA,CAAAI,UAAA,CAAAN,IAAA;MACA,GAAAO,KAAA;QACAT,GAAA,CAAA7B,MAAA,GAAA6B,GAAA,CAAA7B,MAAA;MACA;IACA;IACA;IACA;IACAuC,WAAA,WAAAA,YAAA;MACA,KAAAhD,WAAA,CAAAC,OAAA;MACA,KAAA2B,OAAA;IACA;IACA,aACAqB,UAAA,WAAAA,WAAA;MACA,KAAApD,SAAA;MACA,KAAAqD,SAAA;MACA,KAAAF,WAAA;IACA;IACA;IACAG,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA7D,GAAA,GAAA6D,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAT,MAAA;MAAA;MACA,KAAArD,MAAA,GAAA4D,SAAA,CAAAG,MAAA;MACA,KAAA9D,QAAA,IAAA2D,SAAA,CAAAG,MAAA;IACA;IAGA,eACAC,UAAA,WAAAA,WAAAlB,GAAA;MAAA,IAAAmB,MAAA;MAAA,WAAAC,kBAAA,CAAAvE,OAAA,mBAAAwE,aAAA,CAAAxE,OAAA,IAAAyE,CAAA,UAAAC,QAAA;QAAA,IAAAhB,MAAA,EAAAV,QAAA,EAAA2B,EAAA;QAAA,WAAAH,aAAA,CAAAxE,OAAA,IAAA4E,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA,GAAAD,QAAA,CAAAE,CAAA;YAAA;cACArB,MAAA,GAAAP,GAAA,CAAAO,MAAA;cAAAmB,QAAA,CAAAC,CAAA;cAAAD,QAAA,CAAAE,CAAA;cAAA,OAEA,IAAAC,iBAAA,EAAAtB,MAAA;YAAA;cAAAV,QAAA,GAAA6B,QAAA,CAAAI,CAAA;cACAX,MAAA,CAAA1D,QAAA,GAAAoC,QAAA,CAAA9C,IAAA;cACA;cAAA2E,QAAA,CAAAE,CAAA;cAAA,OACAT,MAAA,CAAAY,iBAAA,CAAAZ,MAAA,CAAA1D,QAAA;YAAA;cACA0D,MAAA,CAAA3D,QAAA;cAAAkE,QAAA,CAAAE,CAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,CAAA;cAAAH,EAAA,GAAAE,QAAA,CAAAI,CAAA;cAEAE,OAAA,CAAAC,KAAA,aAAAT,EAAA;cACAL,MAAA,CAAAf,MAAA,CAAA8B,QAAA;YAAA;cAAA,OAAAR,QAAA,CAAAS,CAAA;UAAA;QAAA,GAAAZ,OAAA;MAAA;IAEA;IACA,aACAQ,iBAAA,WAAAA,kBAAAK,IAAA;MAAA,WAAAhB,kBAAA,CAAAvE,OAAA,mBAAAwE,aAAA,CAAAxE,OAAA,IAAAyE,CAAA,UAAAe,SAAA;QAAA,IAAAC,WAAA,EAAAzC,QAAA,EAAA0C,GAAA;QAAA,WAAAlB,aAAA,CAAAxE,OAAA,IAAA4E,CAAA,WAAAe,SAAA;UAAA,kBAAAA,SAAA,CAAAb,CAAA,GAAAa,SAAA,CAAAZ,CAAA;YAAA;cAAA,IACAQ,IAAA,CAAA9D,aAAA;gBAAAkE,SAAA,CAAAZ,CAAA;gBAAA;cAAA;cACAQ,IAAA,CAAAK,YAAA;cAAA,OAAAD,SAAA,CAAAL,CAAA;YAAA;cAAAK,SAAA,CAAAb,CAAA;cAKAW,WAAA,GAAAF,IAAA,CAAA9D,aAAA,CAAAoE,KAAA,MACAC,MAAA,WAAAC,EAAA;gBAAA,OAAAA,EAAA,CAAAC,IAAA;cAAA,GACA9B,GAAA,WAAA6B,EAAA;gBAAA,OAAAE,QAAA,CAAAF,EAAA,CAAAC,IAAA;cAAA,GACAF,MAAA,WAAAC,EAAA;gBAAA,QAAAG,KAAA,CAAAH,EAAA;cAAA;cAAA,MAEAN,WAAA,CAAArB,MAAA;gBAAAuB,SAAA,CAAAZ,CAAA;gBAAA;cAAA;cACAQ,IAAA,CAAAK,YAAA;cAAA,OAAAD,SAAA,CAAAL,CAAA;YAAA;cAAAK,SAAA,CAAAZ,CAAA;cAAA,OAKA,IAAAoB,8BAAA,EAAAV,WAAA;YAAA;cAAAzC,QAAA,GAAA2C,SAAA,CAAAV,CAAA;cACA,IAAAjC,QAAA,CAAA9C,IAAA,IAAAkG,KAAA,CAAAC,OAAA,CAAArD,QAAA,CAAA9C,IAAA;gBACAqF,IAAA,CAAAK,YAAA,GAAA5C,QAAA,CAAA9C,IAAA,CAAAgE,GAAA,WAAAC,IAAA;kBAAA;oBACA4B,EAAA,EAAA5B,IAAA,CAAA4B,EAAA;oBACAO,QAAA,EAAAnC,IAAA,CAAAmC,QAAA;oBACAC,QAAA,EAAApC,IAAA,CAAAoC,QAAA;oBACAC,SAAA,EAAArC,IAAA,CAAAqC,SAAA;oBACAC,UAAA,EAAAtC,IAAA,CAAAsC,UAAA;oBACAC,QAAA,EAAAvC,IAAA,CAAAuC;kBACA;gBAAA;cACA;gBACAnB,IAAA,CAAAK,YAAA;cACA;cAAAD,SAAA,CAAAZ,CAAA;cAAA;YAAA;cAAAY,SAAA,CAAAb,CAAA;cAAAY,GAAA,GAAAC,SAAA,CAAAV,CAAA;cAEAE,OAAA,CAAAC,KAAA,aAAAM,GAAA;cACAH,IAAA,CAAAK,YAAA;YAAA;cAAA,OAAAD,SAAA,CAAAL,CAAA;UAAA;QAAA,GAAAE,QAAA;MAAA;IAEA;IACA;IACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;IAIA;IACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAEA;IACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACA;IACAmB,aAAA,WAAAA,cAAAxD,GAAA;MAAA,IAAAyD,MAAA;MACA,IAAAC,cAAA;MACA,IAAAC,OAAA;MAEA,IAAA3D,GAAA,CAAAO,MAAA;QACA;QACAoD,OAAA,IAAA3D,GAAA,CAAAO,MAAA;QACA,IAAAJ,WAAA,GAAAH,GAAA,CAAA/B,QAAA,IAAA+B,GAAA,CAAAhC,cAAA,IAAAgC,GAAA,CAAAjC,QAAA;QACA2F,cAAA,iBAAAvD,WAAA;MACA;QACA;QACAwD,OAAA,QAAA1G,GAAA;QACAyG,cAAA,sBAAAzG,GAAA,CAAAgE,MAAA;MACA;MAEA,KAAAb,MAAA,CAAAC,OAAA,CAAAqD,cAAA,EAAA/G,IAAA;QACA;QACA,WAAAiH,0BAAA,EAAAD,OAAA;MACA,GAAAhH,IAAA;QACA8G,MAAA,CAAAnE,OAAA;QACAmE,MAAA,CAAArD,MAAA,CAAAI,UAAA;MACA,GAAAC,KAAA;IACA;IACA,aACAoD,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,4BAAAC,cAAA,CAAAlH,OAAA,MACA,KAAAa,WAAA,WAAAsG,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;IACA,gBACA3E,mBAAA,WAAAA,oBAAA;MACA,IAAA4E,WAAA,OAAAF,IAAA,GAAAG,WAAA;MACA,IAAAC,SAAA;MACA,KAAA9F,eAAA;MACA,SAAA+F,IAAA,GAAAH,WAAA,EAAAG,IAAA,IAAAD,SAAA,EAAAC,IAAA;QACA,KAAA/F,eAAA,CAAAgG,IAAA,CAAAD,IAAA,CAAAE,QAAA;MACA;IACA;IACA,gBACAhF,wBAAA,WAAAA,yBAAA;MAAA,IAAAiF,MAAA;MAAA,WAAArD,kBAAA,CAAAvE,OAAA,mBAAAwE,aAAA,CAAAxE,OAAA,IAAAyE,CAAA,UAAAoD,SAAA;QAAA,IAAA7E,QAAA,EAAA8E,GAAA;QAAA,WAAAtD,aAAA,CAAAxE,OAAA,IAAA4E,CAAA,WAAAmD,SAAA;UAAA,kBAAAA,SAAA,CAAAjD,CAAA,GAAAiD,SAAA,CAAAhD,CAAA;YAAA;cAAAgD,SAAA,CAAAjD,CAAA;cAAAiD,SAAA,CAAAhD,CAAA;cAAA,OAEA,IAAAiD,yBAAA;YAAA;cAAAhF,QAAA,GAAA+E,SAAA,CAAA9C,CAAA;cACA2C,MAAA,CAAAhG,oBAAA,GAAAoB,QAAA,CAAA9C,IAAA;cAAA6H,SAAA,CAAAhD,CAAA;cAAA;YAAA;cAAAgD,SAAA,CAAAjD,CAAA;cAAAgD,GAAA,GAAAC,SAAA,CAAA9C,CAAA;cAEAE,OAAA,CAAAC,KAAA,aAAA0C,GAAA;cACAF,MAAA,CAAAhG,oBAAA;YAAA;cAAA,OAAAmG,SAAA,CAAAzC,CAAA;UAAA;QAAA,GAAAuC,QAAA;MAAA;IAEA;IACA,aACAI,mBAAA,WAAAA,oBAAAC,SAAA;MACA,IAAAA,SAAA;QACA;QACA,IAAAC,WAAA,4RAKA;QAEA,KAAAC,OAAA;UACAC,KAAA;UACAC,wBAAA;UACAnG,OAAA,EAAAgG,WAAA;UACAI,gBAAA;UACAC,iBAAA;UACAC,iBAAA;UACAC,WAAA;QACA;;QAEA;QACA,IAAAC,GAAA,OAAAC,KAAA;QACAD,GAAA,CAAAE,MAAA;UACA;UACA,IAAAC,OAAA,8CAAA3B,MAAA,CAEAe,SAAA,+TAKA;;UAEA;UACA,IAAAa,UAAA,GAAAC,QAAA,CAAAC,aAAA;UACA,IAAAF,UAAA;YACAA,UAAA,CAAAG,SAAA,GAAAJ,OAAA;UACA;QACA;QAEAH,GAAA,CAAAQ,OAAA;UACA;UACA,IAAAC,SAAA,ybAMA;UAEA,IAAAL,UAAA,GAAAC,QAAA,CAAAC,aAAA;UACA,IAAAF,UAAA;YACAA,UAAA,CAAAG,SAAA,GAAAE,SAAA;UACA;QACA;QAEAT,GAAA,CAAAU,GAAA,GAAAnB,SAAA;QACAS,GAAA,CAAAW,cAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}