{"remainingRequest": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\src\\api\\miniapp\\haitang\\projectConfig.js", "dependencies": [{"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\src\\api\\miniapp\\haitang\\projectConfig.js", "mtime": 1754272576486}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1753781940188}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753843467163}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753843480362}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listProjectConfig", "query", "request", "url", "method", "params", "getProjectConfig", "configId", "addProjectConfig", "data", "updateProjectConfig", "delProjectConfig", "enableProjectConfig", "getEnabledProjectConfig"], "sources": ["D:/develop/pythonProject/tjuhaitang_miniapp/ruoyi-ui/src/api/miniapp/haitang/projectConfig.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询项目报名配置列表\nexport function listProjectConfig(query) {\n  return request({\n    url: '/miniapp/haitang/project-config/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询项目报名配置详细\nexport function getProjectConfig(configId) {\n  return request({\n    url: '/miniapp/haitang/project-config/' + configId,\n    method: 'get'\n  })\n}\n\n// 新增项目报名配置\nexport function addProjectConfig(data) {\n  return request({\n    url: '/miniapp/haitang/project-config',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改项目报名配置\nexport function updateProjectConfig(data) {\n  return request({\n    url: '/miniapp/haitang/project-config',\n    method: 'put',\n    data: data\n  })\n}\n\n// 删除项目报名配置\nexport function delProjectConfig(configId) {\n  return request({\n    url: '/miniapp/haitang/project-config/' + configId,\n    method: 'delete'\n  })\n}\n\n// 启用表单配置\nexport function enableProjectConfig(configId) {\n  return request({\n    url: '/miniapp/haitang/project-config/enable/' + configId,\n    method: 'put'\n  })\n}\n\n// 获取启用的表单配置（小程序端）\nexport function getEnabledProjectConfig() {\n  return request({\n    url: '/miniapp/haitang/project-config/app/getEnabledConfig',\n    method: 'get'\n  })\n}\n"], "mappings": ";;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,iBAAiBA,CAACC,KAAK,EAAE;EACvC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,sCAAsC;IAC3CC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,gBAAgBA,CAACC,QAAQ,EAAE;EACzC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,kCAAkC,GAAGI,QAAQ;IAClDH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,gBAAgBA,CAACC,IAAI,EAAE;EACrC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,iCAAiC;IACtCC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,mBAAmBA,CAACD,IAAI,EAAE;EACxC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,iCAAiC;IACtCC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,gBAAgBA,CAACJ,QAAQ,EAAE;EACzC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,kCAAkC,GAAGI,QAAQ;IAClDH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,mBAAmBA,CAACL,QAAQ,EAAE;EAC5C,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,yCAAyC,GAAGI,QAAQ;IACzDH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASS,uBAAuBA,CAAA,EAAG;EACxC,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,sDAAsD;IAC3DC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}