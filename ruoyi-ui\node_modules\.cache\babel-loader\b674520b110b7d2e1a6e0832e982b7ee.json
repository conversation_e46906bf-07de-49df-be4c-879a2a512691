{"remainingRequest": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\src\\api\\miniapp\\haitang\\projectRegistration.js", "dependencies": [{"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\src\\api\\miniapp\\haitang\\projectRegistration.js", "mtime": 1754272587490}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1753781940188}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753843467163}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753843480362}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listProjectRegistration", "query", "request", "url", "method", "params", "getProjectRegistration", "registrationId", "addProjectRegistration", "data", "updateProjectRegistration", "delProjectRegistration", "auditProjectRegistration", "submitProjectRegistration", "getUserProjectRegistration", "userId", "configId"], "sources": ["D:/develop/pythonProject/tjuhaitang_miniapp/ruoyi-ui/src/api/miniapp/haitang/projectRegistration.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询项目报名记录列表\nexport function listProjectRegistration(query) {\n  return request({\n    url: '/miniapp/haitang/project-registration/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询项目报名记录详细\nexport function getProjectRegistration(registrationId) {\n  return request({\n    url: '/miniapp/haitang/project-registration/' + registrationId,\n    method: 'get'\n  })\n}\n\n// 新增项目报名记录\nexport function addProjectRegistration(data) {\n  return request({\n    url: '/miniapp/haitang/project-registration',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改项目报名记录\nexport function updateProjectRegistration(data) {\n  return request({\n    url: '/miniapp/haitang/project-registration',\n    method: 'put',\n    data: data\n  })\n}\n\n// 删除项目报名记录\nexport function delProjectRegistration(registrationId) {\n  return request({\n    url: '/miniapp/haitang/project-registration/' + registrationId,\n    method: 'delete'\n  })\n}\n\n// 审核项目报名记录\nexport function auditProjectRegistration(data) {\n  return request({\n    url: '/miniapp/haitang/project-registration/audit',\n    method: 'put',\n    data: data\n  })\n}\n\n// 提交项目报名（小程序端）\nexport function submitProjectRegistration(data) {\n  return request({\n    url: '/miniapp/haitang/project-registration/app/submit',\n    method: 'post',\n    data: data\n  })\n}\n\n// 查询用户报名记录（小程序端）\nexport function getUserProjectRegistration(userId, configId) {\n  return request({\n    url: '/miniapp/haitang/project-registration/app/getUserRegistration/' + userId + '/' + configId,\n    method: 'get'\n  })\n}\n"], "mappings": ";;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,uBAAuBA,CAACC,KAAK,EAAE;EAC7C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,4CAA4C;IACjDC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,sBAAsBA,CAACC,cAAc,EAAE;EACrD,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,wCAAwC,GAAGI,cAAc;IAC9DH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,sBAAsBA,CAACC,IAAI,EAAE;EAC3C,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,uCAAuC;IAC5CC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,yBAAyBA,CAACD,IAAI,EAAE;EAC9C,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,uCAAuC;IAC5CC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,sBAAsBA,CAACJ,cAAc,EAAE;EACrD,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,wCAAwC,GAAGI,cAAc;IAC9DH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,wBAAwBA,CAACH,IAAI,EAAE;EAC7C,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,6CAA6C;IAClDC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,yBAAyBA,CAACJ,IAAI,EAAE;EAC9C,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,kDAAkD;IACvDC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,0BAA0BA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EAC3D,OAAO,IAAAd,gBAAO,EAAC;IACbC,GAAG,EAAE,gEAAgE,GAAGY,MAAM,GAAG,GAAG,GAAGC,QAAQ;IAC/FZ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}