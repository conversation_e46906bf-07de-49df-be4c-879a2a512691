<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.miniapp.mapper.HaitangProjectRegistrationMapper">
    
    <resultMap type="HaitangProjectRegistration" id="HaitangProjectRegistrationResult">
        <result property="registrationId"    column="registration_id"    />
        <result property="configId"    column="config_id"    />
        <result property="userId"    column="user_id"    />
        <result property="userNickName"    column="user_nick_name"    />
        <result property="userAvatarUrl"    column="user_avatar_url"    />
        <result property="formData"    column="form_data"    />
        <result property="auditStatus"    column="audit_status"    />
        <result property="auditRemark"    column="audit_remark"    />
        <result property="auditBy"    column="audit_by"    />
        <result property="auditTime"    column="audit_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectHaitangProjectRegistrationVo">
        select registration_id, config_id, user_id, user_nick_name, user_avatar_url, form_data, audit_status, audit_remark, audit_by, audit_time, create_by, create_time, update_by, update_time, remark from haitang_project_registration
    </sql>

    <select id="selectHaitangProjectRegistrationList" parameterType="HaitangProjectRegistration" resultMap="HaitangProjectRegistrationResult">
        <include refid="selectHaitangProjectRegistrationVo"/>
        <where>  
            <if test="configId != null "> and config_id = #{configId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="userNickName != null  and userNickName != ''"> and user_nick_name like concat('%', #{userNickName}, '%')</if>
            <if test="auditStatus != null  and auditStatus != ''"> and audit_status = #{auditStatus}</if>
            <if test="auditBy != null  and auditBy != ''"> and audit_by like concat('%', #{auditBy}, '%')</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectHaitangProjectRegistrationByRegistrationId" parameterType="Long" resultMap="HaitangProjectRegistrationResult">
        <include refid="selectHaitangProjectRegistrationVo"/>
        where registration_id = #{registrationId}
    </select>
        
    <insert id="insertHaitangProjectRegistration" parameterType="HaitangProjectRegistration" useGeneratedKeys="true" keyProperty="registrationId">
        insert into haitang_project_registration
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="configId != null">config_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="userNickName != null">user_nick_name,</if>
            <if test="userAvatarUrl != null">user_avatar_url,</if>
            <if test="formData != null">form_data,</if>
            <if test="auditStatus != null">audit_status,</if>
            <if test="auditRemark != null">audit_remark,</if>
            <if test="auditBy != null">audit_by,</if>
            <if test="auditTime != null">audit_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="configId != null">#{configId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="userNickName != null">#{userNickName},</if>
            <if test="userAvatarUrl != null">#{userAvatarUrl},</if>
            <if test="formData != null">#{formData},</if>
            <if test="auditStatus != null">#{auditStatus},</if>
            <if test="auditRemark != null">#{auditRemark},</if>
            <if test="auditBy != null">#{auditBy},</if>
            <if test="auditTime != null">#{auditTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateHaitangProjectRegistration" parameterType="HaitangProjectRegistration">
        update haitang_project_registration
        <trim prefix="SET" suffixOverrides=",">
            <if test="configId != null">config_id = #{configId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userNickName != null">user_nick_name = #{userNickName},</if>
            <if test="userAvatarUrl != null">user_avatar_url = #{userAvatarUrl},</if>
            <if test="formData != null">form_data = #{formData},</if>
            <if test="auditStatus != null">audit_status = #{auditStatus},</if>
            <if test="auditRemark != null">audit_remark = #{auditRemark},</if>
            <if test="auditBy != null">audit_by = #{auditBy},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where registration_id = #{registrationId}
    </update>

    <delete id="deleteHaitangProjectRegistrationByRegistrationId" parameterType="Long">
        delete from haitang_project_registration where registration_id = #{registrationId}
    </delete>

    <delete id="deleteHaitangProjectRegistrationByRegistrationIds" parameterType="String">
        delete from haitang_project_registration where registration_id in 
        <foreach item="registrationId" collection="array" open="(" separator="," close=")">
            #{registrationId}
        </foreach>
    </delete>

    <select id="selectByUserIdAndConfigId" resultMap="HaitangProjectRegistrationResult">
        <include refid="selectHaitangProjectRegistrationVo"/>
        where user_id = #{userId} and config_id = #{configId}
        limit 1
    </select>

    <update id="auditRegistration" parameterType="HaitangProjectRegistration">
        update haitang_project_registration
        set audit_status = #{auditStatus},
            audit_remark = #{auditRemark},
            audit_by = #{auditBy},
            audit_time = #{auditTime}
        where registration_id = #{registrationId}
    </update>

</mapper>
