{"remainingRequest": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\user\\index.vue?vue&type=style&index=0&id=35a4206d&scoped=true&lang=css", "dependencies": [{"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\user\\index.vue", "mtime": 1754036860043}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1753843472439}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1753843491236}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1753843480355}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753843467163}, {"path": "D:\\develop\\pythonProject\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753843484659}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQoudXNlci1kZXRhaWwtY29udGFpbmVyIHsNCiAgbWF4LWhlaWdodDogNzB2aDsNCiAgb3ZlcmZsb3cteTogYXV0bzsNCn0NCg0KLmRldGFpbC1jYXJkIHsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KfQ0KDQouZGV0YWlsLWNhcmQ6bGFzdC1jaGlsZCB7DQogIG1hcmdpbi1ib3R0b206IDA7DQp9DQoNCi5jYXJkLWhlYWRlciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIHBhZGRpbmc6IDA7DQp9DQoNCi5jYXJkLXRpdGxlIHsNCiAgZm9udC1zaXplOiAxNnB4Ow0KICBmb250LXdlaWdodDogNjAwOw0KICBjb2xvcjogIzMwMzEzMzsNCiAgcG9zaXRpb246IHJlbGF0aXZlOw0KICBwYWRkaW5nLWxlZnQ6IDEycHg7DQp9DQoNCi5jYXJkLXRpdGxlOjpiZWZvcmUgew0KICBjb250ZW50OiAnJzsNCiAgcG9zaXRpb246IGFic29sdXRlOw0KICBsZWZ0OiAwOw0KICB0b3A6IDUwJTsNCiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC01MCUpOw0KICB3aWR0aDogNHB4Ow0KICBoZWlnaHQ6IDE2cHg7DQogIGJhY2tncm91bmQ6ICM0MDlFRkY7DQogIGJvcmRlci1yYWRpdXM6IDJweDsNCn0NCg0KLmF2YXRhci1pdGVtIHsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KICBwYWRkaW5nOiAyMHB4Ow0KfQ0KDQouYXZhdGFyLWxhYmVsIHsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBjb2xvcjogIzYwNjI2NjsNCiAgbWFyZ2luLWJvdHRvbTogMTVweDsNCiAgZm9udC13ZWlnaHQ6IDUwMDsNCn0NCg0KLmF2YXRhci1jb250ZW50IHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIG1pbi1oZWlnaHQ6IDgwcHg7DQp9DQoNCi5uby1hdmF0YXIgew0KICBjb2xvcjogI0MwQzRDQzsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgd2lkdGg6IDgwcHg7DQogIGhlaWdodDogODBweDsNCiAgYm9yZGVyOiAycHggZGFzaGVkICNFNEU3RUQ7DQogIGJvcmRlci1yYWRpdXM6IDUwJTsNCiAgYmFja2dyb3VuZC1jb2xvcjogI0ZBRkFGQTsNCn0NCg0KLyog5ZON5bqU5byP6K6+6K6hICovDQpAbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHsNCiAgLnVzZXItZGV0YWlsLWNvbnRhaW5lciB7DQogICAgbWF4LWhlaWdodDogNjB2aDsNCiAgfQ0KDQogIC5hdmF0YXItaXRlbSB7DQogICAgcGFkZGluZzogMTVweDsNCiAgfQ0KDQogIC5hdmF0YXItY29udGVudCB7DQogICAgbWluLWhlaWdodDogNjBweDsNCiAgfQ0KDQogIC5uby1hdmF0YXIgew0KICAgIHdpZHRoOiA2MHB4Ow0KICAgIGhlaWdodDogNjBweDsNCiAgfQ0KDQogIC5pbmR1c3RyeS1maWVsZC1jb250YWluZXIgLmluZHVzdHJ5LXRhZ3MtZGlzcGxheSB7DQogICAgbWFyZ2luLWJvdHRvbTogOHB4Ow0KICB9DQoNCiAgLmluZHVzdHJ5LXRhZ3MgLmVsLXRhZyB7DQogICAgbWFyZ2luLXJpZ2h0OiA1cHg7DQogICAgbWFyZ2luLWJvdHRvbTogNXB4Ow0KICB9DQoNCiAgLnBlcnNvbmFsLWludHJvIHsNCiAgICBtYXgtd2lkdGg6IDEwMCU7DQogICAgd29yZC13cmFwOiBicmVhay13b3JkOw0KICAgIHdoaXRlLXNwYWNlOiBwcmUtd3JhcDsNCiAgICBsaW5lLWhlaWdodDogMS41Ow0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqvBA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/miniapp/user", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!--用户数据-->\r\n        <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n          <el-form-item label=\"关键字\" prop=\"searchValue\">\r\n            <el-input\r\n              v-model=\"queryParams.searchValue\"\r\n              placeholder=\"搜索姓名、昵称、手机号等\"\r\n              clearable\r\n              style=\"width: 280px\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            >\r\n              <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n            </el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"用户名称\" prop=\"userName\">\r\n            <el-input\r\n              v-model=\"queryParams.userName\"\r\n              placeholder=\"用户名称\"\r\n              clearable\r\n              style=\"width: 160px\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"微信昵称\" prop=\"weixinNickname\">\r\n            <el-input\r\n              v-model=\"queryParams.weixinNickname\"\r\n              placeholder=\"微信昵称\"\r\n              clearable\r\n              style=\"width: 160px\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"真实姓名\" prop=\"realName\">\r\n            <el-input\r\n              v-model=\"queryParams.realName\"\r\n              placeholder=\"真实姓名\"\r\n              clearable\r\n              style=\"width: 160px\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"手机号码\" prop=\"phonenumber\">\r\n            <el-input\r\n              v-model=\"queryParams.phonenumber\"\r\n              placeholder=\"手机号码\"\r\n              clearable\r\n              style=\"width: 160px\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"状态\" prop=\"status\">\r\n            <el-select\r\n              v-model=\"queryParams.status\"\r\n              placeholder=\"用户状态\"\r\n              clearable\r\n              style=\"width: 150px\"\r\n            >\r\n              <el-option\r\n                v-for=\"dict in dict.type.sys_normal_disable\"\r\n                :key=\"dict.value\"\r\n                :label=\"dict.label\"\r\n                :value=\"dict.value\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"毕业年份\" prop=\"graduationYear\">\r\n            <el-select\r\n              v-model=\"queryParams.graduationYear\"\r\n              placeholder=\"请选择毕业年份\"\r\n              clearable\r\n              style=\"width: 150px\"\r\n            >\r\n              <el-option\r\n                v-for=\"year in graduationYears\"\r\n                :key=\"year\"\r\n                :label=\"year + '年'\"\r\n                :value=\"year\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"地区\" prop=\"region\">\r\n            <el-select\r\n              v-model=\"queryParams.region\"\r\n              placeholder=\"请选择地区\"\r\n              clearable\r\n              filterable\r\n              style=\"width: 150px\"\r\n            >\r\n              <el-option\r\n                v-for=\"province in provinces\"\r\n                :key=\"province\"\r\n                :label=\"province\"\r\n                :value=\"province\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"行业领域\" prop=\"industryField\">\r\n            <el-select\r\n              v-model=\"queryParams.industryField\"\r\n              placeholder=\"请选择行业领域\"\r\n              clearable\r\n              filterable\r\n              style=\"width: 150px\"\r\n            >\r\n              <el-option\r\n                v-for=\"industry in firstLevelIndustries\"\r\n                :key=\"industry.id\"\r\n                :label=\"industry.nodeName\"\r\n                :value=\"industry.id\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"创建时间\">\r\n            <el-date-picker\r\n              v-model=\"dateRange\"\r\n              style=\"width: 200px\"\r\n              value-format=\"yyyy-MM-dd\"\r\n              type=\"daterange\"\r\n              range-separator=\"-\"\r\n              start-placeholder=\"开始日期\"\r\n              end-placeholder=\"结束日期\"\r\n            ></el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <!-- 修改按钮已隐藏：用户信息不应由管理员修改 -->\r\n            <!-- <el-button\r\n              type=\"success\"\r\n              plain\r\n              icon=\"el-icon-edit\"\r\n              size=\"mini\"\r\n              :disabled=\"single\"\r\n              @click=\"handleUpdate\"\r\n              v-hasPermi=\"['miniapp:user:edit']\"\r\n            >修改</el-button> -->\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"warning\"\r\n              plain\r\n              icon=\"el-icon-close\"\r\n              size=\"mini\"\r\n              :disabled=\"multiple\"\r\n              @click=\"handleDisable\"\r\n              v-hasPermi=\"['miniapp:user:edit']\"\r\n            >停用</el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"warning\"\r\n              plain\r\n              icon=\"el-icon-download\"\r\n              size=\"mini\"\r\n              @click=\"handleExport\"\r\n              v-hasPermi=\"['miniapp:user:export']\"\r\n            >导出</el-button>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\" :columns=\"columns\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table v-loading=\"loading\" :data=\"userList\" @selection-change=\"handleSelectionChange\">\r\n          <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\r\n          <el-table-column label=\"用户编号\" align=\"center\" key=\"userId\" prop=\"userId\" v-if=\"columns[0].visible\" width=\"80\" />\r\n          <el-table-column label=\"微信昵称\" align=\"center\" key=\"weixinNickname\" prop=\"weixinNickname\" v-if=\"columns[1].visible\" :show-overflow-tooltip=\"true\" />\r\n          <el-table-column label=\"微信头像\" align=\"center\" key=\"weixinAvatar\" v-if=\"columns[2].visible\" width=\"80\">\r\n            <template slot-scope=\"scope\">\r\n              <img v-if=\"scope.row.weixinAvatar\" :src=\"scope.row.weixinAvatar\" alt=\"\" referrerpolicy=\"no-referrer\" style=\"width: 40px; height: 40px; border-radius: 50%; object-fit: cover;\" />\r\n              <el-avatar v-else :size=\"40\" icon=\"el-icon-user-solid\"></el-avatar>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"姓名\" align=\"center\" key=\"realName\" prop=\"realName\" v-if=\"columns[3].visible\" :show-overflow-tooltip=\"true\" />\r\n          <el-table-column label=\"手机号码\" align=\"center\" key=\"phonenumber\" prop=\"phonenumber\" v-if=\"columns[4].visible\" width=\"120\" />\r\n          <el-table-column label=\"形象照\" align=\"center\" key=\"portraitUrl\" v-if=\"columns[5].visible\" width=\"80\">\r\n            <template slot-scope=\"scope\">\r\n              <image-preview :src=\"scope.row.portraitUrl\" :width=\"50\" :height=\"50\" v-if=\"scope.row.portraitUrl\"/>\r\n              <el-avatar v-else :size=\"50\" icon=\"el-icon-picture\"></el-avatar>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"毕业院校\" align=\"center\" key=\"graduateSchool\" prop=\"graduateSchool\" v-if=\"columns[6].visible\" :show-overflow-tooltip=\"true\" />\r\n          <el-table-column label=\"所属企业\" align=\"center\" key=\"currentCompany\" prop=\"currentCompany\" v-if=\"columns[7].visible\" :show-overflow-tooltip=\"true\" />\r\n          <el-table-column label=\"行业领域\" align=\"center\" key=\"industryField\" v-if=\"columns[8].visible\" :show-overflow-tooltip=\"true\">\r\n            <template slot-scope=\"scope\">\r\n              <span v-if=\"scope.row.industryNames\">{{ scope.row.industryNames }}</span>\r\n              <span v-else style=\"color: #C0C4CC;\">未设置</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"状态\" align=\"center\" key=\"status\" v-if=\"columns[9].visible\">\r\n            <template slot-scope=\"scope\">\r\n              <el-switch\r\n                v-model=\"scope.row.status\"\r\n                active-value=\"0\"\r\n                inactive-value=\"1\"\r\n                @change=\"handleStatusChange(scope.row)\"\r\n              ></el-switch>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" v-if=\"columns[10].visible\" width=\"160\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.createTime) }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column\r\n            label=\"操作\"\r\n            align=\"center\"\r\n            width=\"180\"\r\n            class-name=\"small-padding fixed-width\"\r\n          >\r\n            <template slot-scope=\"scope\" v-if=\"scope.row.userId !== 1\">\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-view\"\r\n                @click=\"handleView(scope.row)\"\r\n                v-hasPermi=\"['miniapp:user:query']\"\r\n              >详情</el-button>\r\n              <!-- 修改按钮已隐藏：用户信息不应由管理员修改 -->\r\n              <!-- <el-button\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-edit\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n                v-hasPermi=\"['miniapp:user:edit']\"\r\n              >修改</el-button> -->\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-close\"\r\n                @click=\"handleDisable(scope.row)\"\r\n                v-hasPermi=\"['miniapp:user:edit']\"\r\n                :disabled=\"scope.row.status === '1'\"\r\n              >停用</el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :total=\"total\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          @pagination=\"getList\"\r\n        />\r\n\r\n    <!-- 修改用户对话框已移除：用户信息不应由管理员修改 -->\r\n\r\n\r\n    <!-- 用户详情对话框 -->\r\n    <el-dialog title=\"用户详情\" :visible.sync=\"openView\" width=\"1000px\" append-to-body>\r\n      <div class=\"user-detail-container\">\r\n        <!-- 基本信息 -->\r\n        <el-card class=\"detail-card\" shadow=\"never\">\r\n          <div slot=\"header\" class=\"card-header\">\r\n            <span class=\"card-title\">基本信息</span>\r\n          </div>\r\n          <el-descriptions :column=\"3\" border>\r\n            <el-descriptions-item label=\"用户编号\">{{ viewForm.userId }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"姓名\">{{ viewForm.realName || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"微信昵称\">{{ viewForm.weixinNickname || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"手机号码\">{{ viewForm.phonenumber || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"性别\">\r\n              <dict-tag :options=\"dict.type.sys_user_sex\" :value=\"viewForm.sex\"/>\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"出生日期\">{{ parseTime(viewForm.birthDate, '{y}-{m}-{d}') || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"籍贯\">{{ viewForm.region || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"状态\">\r\n              <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"viewForm.status\"/>\r\n            </el-descriptions-item>\r\n          </el-descriptions>\r\n        </el-card>\r\n\r\n        <!-- 头像信息 -->\r\n        <el-card class=\"detail-card\" shadow=\"never\">\r\n          <div slot=\"header\" class=\"card-header\">\r\n            <span class=\"card-title\">头像信息</span>\r\n          </div>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <div class=\"avatar-item\">\r\n                <div class=\"avatar-label\">微信头像</div>\r\n                <div class=\"avatar-content\">\r\n                  <img v-if=\"viewForm.weixinAvatar\" :src=\"viewForm.weixinAvatar\" alt=\"\" referrerpolicy=\"no-referrer\" style=\"width: 80px; height: 80px; border-radius: 8px; object-fit: cover; cursor: pointer;\" @click=\"previewWeixinAvatar(viewForm.weixinAvatar)\" />\r\n                  <div v-else class=\"no-avatar\">未设置</div>\r\n                </div>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <div class=\"avatar-item\">\r\n                <div class=\"avatar-label\">形象照</div>\r\n                <div class=\"avatar-content\">\r\n                  <image-preview :src=\"viewForm.portraitUrl\" :width=\"80\" :height=\"80\" v-if=\"viewForm.portraitUrl\"/>\r\n                  <div v-else class=\"no-avatar\">未设置</div>\r\n                </div>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n        </el-card>\r\n\r\n        <!-- 教育背景 -->\r\n        <el-card class=\"detail-card\" shadow=\"never\">\r\n          <div slot=\"header\" class=\"card-header\">\r\n            <span class=\"card-title\">教育背景</span>\r\n          </div>\r\n          <el-descriptions :column=\"2\" border>\r\n            <el-descriptions-item label=\"毕业院校\">{{ viewForm.graduateSchool || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"毕业年份\">{{ viewForm.graduationYear || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"专业\">{{ viewForm.major || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"学院\">{{ viewForm.college || '未设置' }}</el-descriptions-item>\r\n          </el-descriptions>\r\n        </el-card>\r\n\r\n        <!-- 职业信息 -->\r\n        <el-card class=\"detail-card\" shadow=\"never\">\r\n          <div slot=\"header\" class=\"card-header\">\r\n            <span class=\"card-title\">职业信息</span>\r\n          </div>\r\n          <el-descriptions :column=\"2\" border>\r\n            <el-descriptions-item label=\"当前公司\">{{ viewForm.currentCompany || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"行业领域\">\r\n              <div v-if=\"viewForm.industryTags && viewForm.industryTags.length > 0\" class=\"industry-tags\">\r\n                <el-tag\r\n                  v-for=\"tag in viewForm.industryTags\"\r\n                  :key=\"tag.id\"\r\n                  size=\"small\"\r\n                  style=\"margin-right: 5px; margin-bottom: 5px;\"\r\n                >\r\n                  {{ tag.nodeName }}\r\n                </el-tag>\r\n              </div>\r\n              <span v-else>未设置</span>\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"职位名称\">{{ viewForm.positionTitle || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"个人介绍\" :span=\"2\">\r\n              <div class=\"personal-intro\">\r\n                {{ viewForm.personalIntroduction || '未设置' }}\r\n              </div>\r\n            </el-descriptions-item>\r\n          </el-descriptions>\r\n        </el-card>\r\n\r\n        <!-- 积分信息 -->\r\n        <el-card class=\"detail-card\" shadow=\"never\">\r\n          <div slot=\"header\" class=\"card-header\">\r\n            <span class=\"card-title\">积分信息</span>\r\n          </div>\r\n          <el-descriptions :column=\"1\" border>\r\n            <el-descriptions-item label=\"总积分\">{{ viewForm.totalPoints || 0 }}</el-descriptions-item>\r\n          </el-descriptions>\r\n        </el-card>\r\n\r\n        <!-- 系统信息 -->\r\n        <el-card class=\"detail-card\" shadow=\"never\">\r\n          <div slot=\"header\" class=\"card-header\">\r\n            <span class=\"card-title\">系统信息</span>\r\n          </div>\r\n          <el-descriptions :column=\"2\" border>\r\n            <el-descriptions-item label=\"最后登录时间\">{{ parseTime(viewForm.lastLoginTime) || '未登录' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"创建时间\">{{ parseTime(viewForm.createTime) || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"更新时间\">{{ parseTime(viewForm.updateTime) || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"备注\" :span=\"2\">{{ viewForm.remark || '无备注' }}</el-descriptions-item>\r\n          </el-descriptions>\r\n        </el-card>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"openView = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listMiniUserAdmin, getMiniUser, changeMiniUserStatus, batchDisableMiniUser } from \"@/api/miniapp/user\";\r\nimport { getNodesByLevel, getBatchIndustryInfo } from \"@/api/miniapp/industry\";\r\n\r\nexport default {\r\n  name: \"MiniUser\",\r\n  dicts: ['sys_normal_disable', 'sys_user_sex'],\r\n  components: {\r\n    ImageUpload: () => import(\"@/components/ImageUpload\"),\r\n    ImagePreview: () => import(\"@/components/ImagePreview\")\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 用户表格数据\r\n      userList: [],\r\n      // 日期范围\r\n      dateRange: [],\r\n      // 用户详情对话框\r\n      openView: false,\r\n      // 用户详情数据\r\n      viewForm: {},\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        searchValue: undefined,\r\n        userName: undefined,\r\n        weixinNickname: undefined,\r\n        realName: undefined,\r\n        phonenumber: undefined,\r\n        status: undefined,\r\n        graduationYear: undefined,\r\n        region: undefined,\r\n        industryField: undefined\r\n      },\r\n      // 毕业年份选项\r\n      graduationYears: [],\r\n      // 省份选项\r\n      provinces: [\r\n        '北京', '天津', '河北', '山西', '内蒙古', '辽宁', '吉林', '黑龙江',\r\n        '上海', '江苏', '浙江', '安徽', '福建', '江西', '山东', '河南',\r\n        '湖北', '湖南', '广东', '广西', '海南', '重庆', '四川', '贵州',\r\n        '云南', '西藏', '陕西', '甘肃', '青海', '宁夏', '新疆', '台湾',\r\n        '香港', '澳门'\r\n      ],\r\n      // 一级行业选项\r\n      firstLevelIndustries: [],\r\n      // 列信息\r\n      columns: [\r\n        { key: 0, label: `用户编号`, visible: true },\r\n        { key: 1, label: `微信昵称`, visible: true },\r\n        { key: 2, label: `微信头像`, visible: true },\r\n        { key: 3, label: `姓名`, visible: true },\r\n        { key: 4, label: `手机号码`, visible: true },\r\n        { key: 5, label: `形象照`, visible: true },\r\n        { key: 6, label: `毕业院校`, visible: true },\r\n        { key: 7, label: `所属企业`, visible: true },\r\n        { key: 8, label: `行业领域`, visible: true },\r\n        { key: 9, label: `状态`, visible: true },\r\n        { key: 10, label: `创建时间`, visible: true }\r\n      ],\r\n      // 表单校验\r\n      rules: {\r\n        weixinNickname: [\r\n          { required: true, message: \"微信昵称不能为空\", trigger: \"blur\" },\r\n          { min: 1, max: 30, message: \"微信昵称长度必须在1到30个字符之间\", trigger: \"blur\" }\r\n        ],\r\n        realName: [\r\n          { required: true, message: \"姓名不能为空\", trigger: \"blur\" },\r\n          { min: 2, max: 30, message: \"姓名长度必须在2到30个字符之间\", trigger: \"blur\" }\r\n        ],\r\n        phonenumber: [\r\n          {\r\n            pattern: /^1[3-9]\\d{9}$/,\r\n            message: \"请输入正确的手机号码\",\r\n            trigger: \"blur\"\r\n          }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n    this.initGraduationYears();\r\n    this.initFirstLevelIndustries();\r\n  },\r\n  methods: {\r\n    /** 查询用户列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      // 后台管理使用新的 API，查询所有用户（包括停用的）\r\n      listMiniUserAdmin(this.addDateRange(this.queryParams, this.dateRange)).then(response => {\r\n        this.userList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 用户状态修改\r\n    handleStatusChange(row) {\r\n      let text = row.status === \"0\" ? \"启用\" : \"停用\";\r\n      // 使用真实姓名或微信昵称作为显示名称\r\n      let displayName = row.realName || row.weixinNickname || row.userName || '该用户';\r\n      this.$modal.confirm('确认要\"' + text + '\"\"' + displayName + '\"用户吗？').then(function() {\r\n        return changeMiniUserStatus(row.userId, row.status);\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + \"成功\");\r\n      }).catch(function() {\r\n        row.status = row.status === \"0\" ? \"1\" : \"0\";\r\n      });\r\n    },\r\n    // 取消按钮和表单重置方法已移除：不再需要修改功能\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = [];\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.userId);\r\n      this.single = selection.length != 1;\r\n      this.multiple = !selection.length;\r\n    },\r\n\r\n\r\n    /** 查看详情按钮操作 */\r\n    async handleView(row) {\r\n      const userId = row.userId;\r\n      try {\r\n        const response = await getMiniUser(userId);\r\n        this.viewForm = response.data;\r\n        // 解析行业标签\r\n        await this.parseIndustryTags(this.viewForm);\r\n        this.openView = true;\r\n      } catch (error) {\r\n        console.error('获取用户详情失败', error);\r\n        this.$modal.msgError('获取用户详情失败');\r\n      }\r\n    },\r\n    /** 解析行业标签 */\r\n    async parseIndustryTags(user) {\r\n      if (!user.industryField) {\r\n        user.industryTags = [];\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const industryIds = user.industryField.split(',')\r\n          .filter(id => id.trim())\r\n          .map(id => parseInt(id.trim()))\r\n          .filter(id => !isNaN(id));\r\n\r\n        if (industryIds.length === 0) {\r\n          user.industryTags = [];\r\n          return;\r\n        }\r\n\r\n        // 批量查询行业信息\r\n        const response = await getBatchIndustryInfo(industryIds);\r\n        if (response.data && Array.isArray(response.data)) {\r\n          user.industryTags = response.data.map(item => ({\r\n            id: item.id,\r\n            nodeName: item.nodeName,\r\n            nodeType: item.nodeType,\r\n            nodeLevel: item.nodeLevel,\r\n            streamType: item.streamType,\r\n            rootNode: item.rootNode\r\n          }));\r\n        } else {\r\n          user.industryTags = [];\r\n        }\r\n      } catch (error) {\r\n        console.error('解析行业标签失败', error);\r\n        user.industryTags = [];\r\n      }\r\n    },\r\n    /** 解析编辑表单中的行业标签 - 已禁用 */\r\n    /*\r\n    async parseFormIndustryTags() {\r\n      if (!this.form.industryField) {\r\n        this.form.industryTags = [];\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const industryIds = this.form.industryField.split(',').filter(id => id.trim());\r\n        const industryTags = [];\r\n\r\n        for (const industryId of industryIds) {\r\n          if (industryId.trim()) {\r\n            try {\r\n              const response = await getIndustryNodeInfo(industryId.trim());\r\n              if (response.data) {\r\n                industryTags.push({\r\n                  id: response.data.id,\r\n                  nodeName: response.data.nodeName,\r\n                  nodeType: response.data.nodeType,\r\n                  nodeLevel: response.data.nodeLevel\r\n                });\r\n              }\r\n            } catch (error) {\r\n              console.warn(`获取行业信息失败，ID: ${industryId}`, error);\r\n            }\r\n          }\r\n        }\r\n\r\n        this.form.industryTags = industryTags;\r\n      } catch (error) {\r\n        console.error('解析编辑表单行业标签失败', error);\r\n        this.form.industryTags = [];\r\n      }\r\n    },\r\n    */\r\n    /** 修改按钮操作 - 已禁用 */\r\n    /*\r\n    async handleUpdate(row) {\r\n      this.reset();\r\n      const userId = row.userId || this.ids;\r\n      try {\r\n        const response = await getMiniUser(userId);\r\n        this.form = response.data;\r\n        // 解析行业标签\r\n        await this.parseFormIndustryTags();\r\n        this.open = true;\r\n        this.title = \"修改用户\";\r\n        this.form.password = \"\";\r\n      } catch (error) {\r\n        console.error('获取用户信息失败', error);\r\n        this.$modal.msgError('获取用户信息失败');\r\n      }\r\n    },\r\n    */\r\n\r\n    /** 提交按钮 - 已禁用 */\r\n    /*\r\n    submitForm: function() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.userId != undefined) {\r\n            updateMiniUser(this.form).then(() => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    */\r\n    /** 停用按钮操作 */\r\n    handleDisable(row) {\r\n      let confirmMessage;\r\n      let userIds = [];\r\n\r\n      if (row.userId) {\r\n        // 单个停用\r\n        userIds = [row.userId];\r\n        let displayName = row.realName || row.weixinNickname || row.userName || '该用户';\r\n        confirmMessage = '是否确认停用用户\"' + displayName + '\"？停用后该用户将无法登录小程序。';\r\n      } else {\r\n        // 批量停用\r\n        userIds = this.ids;\r\n        confirmMessage = '是否确认停用选中的' + this.ids.length + '个用户？停用后这些用户将无法登录小程序。';\r\n      }\r\n\r\n      this.$modal.confirm(confirmMessage).then(() => {\r\n        // 调用批量停用API\r\n        return batchDisableMiniUser(userIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"停用成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('miniapp/user/export', {\r\n        ...this.queryParams\r\n      }, `user_${new Date().getTime()}.xlsx`)\r\n    },\r\n    /** 初始化毕业年份选项 */\r\n    initGraduationYears() {\r\n      const currentYear = new Date().getFullYear();\r\n      const startYear = 1980;\r\n      this.graduationYears = [];\r\n      for (let year = currentYear; year >= startYear; year--) {\r\n        this.graduationYears.push(year.toString());\r\n      }\r\n    },\r\n    /** 初始化一级行业选项 */\r\n    async initFirstLevelIndustries() {\r\n      try {\r\n        const response = await getNodesByLevel(1);\r\n        this.firstLevelIndustries = response.data || [];\r\n      } catch (error) {\r\n        console.error('获取一级行业失败', error);\r\n        this.firstLevelIndustries = [];\r\n      }\r\n    },\r\n    /** 预览微信头像 */\r\n    previewWeixinAvatar(avatarUrl) {\r\n      if (avatarUrl) {\r\n        // 先显示加载中的对话框\r\n        const loadingHtml = `\r\n          <div style=\"text-align: center; padding: 20px;\">\r\n            <i class=\"el-icon-loading\" style=\"font-size: 24px; color: #409EFF;\"></i>\r\n            <div style=\"margin-top: 10px; color: #666;\">头像加载中...</div>\r\n          </div>\r\n        `;\r\n\r\n        this.$msgbox({\r\n          title: '微信头像预览',\r\n          dangerouslyUseHTMLString: true,\r\n          message: loadingHtml,\r\n          showCancelButton: false,\r\n          showConfirmButton: true,\r\n          confirmButtonText: '关闭',\r\n          customClass: 'avatar-preview-dialog'\r\n        });\r\n\r\n        // 预加载图片\r\n        const img = new Image();\r\n        img.onload = () => {\r\n          // 图片加载成功后更新对话框内容\r\n          const imgHtml = `\r\n            <img\r\n              src=\"${avatarUrl}\"\r\n              alt=\"微信头像预览\"\r\n              referrerpolicy=\"no-referrer\"\r\n              style=\"max-width: 100%; max-height: 400px; object-fit: contain; display: block; margin: 0 auto; border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\"\r\n            />\r\n          `;\r\n\r\n          // 更新对话框内容\r\n          const messageBox = document.querySelector('.avatar-preview-dialog .el-message-box__message');\r\n          if (messageBox) {\r\n            messageBox.innerHTML = imgHtml;\r\n          }\r\n        };\r\n\r\n        img.onerror = () => {\r\n          // 图片加载失败\r\n          const errorHtml = `\r\n            <div style=\"text-align: center; padding: 20px; color: #F56C6C;\">\r\n              <i class=\"el-icon-picture-outline\" style=\"font-size: 48px; margin-bottom: 10px;\"></i>\r\n              <div>头像加载失败</div>\r\n              <div style=\"font-size: 12px; margin-top: 5px; color: #999;\">请检查网络连接或图片链接</div>\r\n            </div>\r\n          `;\r\n\r\n          const messageBox = document.querySelector('.avatar-preview-dialog .el-message-box__message');\r\n          if (messageBox) {\r\n            messageBox.innerHTML = errorHtml;\r\n          }\r\n        };\r\n\r\n        img.src = avatarUrl;\r\n        img.referrerPolicy = \"no-referrer\";\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.user-detail-container {\r\n  max-height: 70vh;\r\n  overflow-y: auto;\r\n}\r\n\r\n.detail-card {\r\n  margin-bottom: 20px;\r\n  border-radius: 8px;\r\n}\r\n\r\n.detail-card:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0;\r\n}\r\n\r\n.card-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n  position: relative;\r\n  padding-left: 12px;\r\n}\r\n\r\n.card-title::before {\r\n  content: '';\r\n  position: absolute;\r\n  left: 0;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  width: 4px;\r\n  height: 16px;\r\n  background: #409EFF;\r\n  border-radius: 2px;\r\n}\r\n\r\n.avatar-item {\r\n  text-align: center;\r\n  padding: 20px;\r\n}\r\n\r\n.avatar-label {\r\n  font-size: 14px;\r\n  color: #606266;\r\n  margin-bottom: 15px;\r\n  font-weight: 500;\r\n}\r\n\r\n.avatar-content {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  min-height: 80px;\r\n}\r\n\r\n.no-avatar {\r\n  color: #C0C4CC;\r\n  font-size: 14px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 80px;\r\n  height: 80px;\r\n  border: 2px dashed #E4E7ED;\r\n  border-radius: 50%;\r\n  background-color: #FAFAFA;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .user-detail-container {\r\n    max-height: 60vh;\r\n  }\r\n\r\n  .avatar-item {\r\n    padding: 15px;\r\n  }\r\n\r\n  .avatar-content {\r\n    min-height: 60px;\r\n  }\r\n\r\n  .no-avatar {\r\n    width: 60px;\r\n    height: 60px;\r\n  }\r\n\r\n  .industry-field-container .industry-tags-display {\r\n    margin-bottom: 8px;\r\n  }\r\n\r\n  .industry-tags .el-tag {\r\n    margin-right: 5px;\r\n    margin-bottom: 5px;\r\n  }\r\n\r\n  .personal-intro {\r\n    max-width: 100%;\r\n    word-wrap: break-word;\r\n    white-space: pre-wrap;\r\n    line-height: 1.5;\r\n  }\r\n}\r\n</style>\r\n"]}]}